# 文件传输服务配置示例 (简化版本 - 仅支持S3风格API)

spring:
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: ********************************************

file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      
      # 清理配置
      cleanup-interval: 3600000        # 1小时（毫秒）
      record-expire-time: 86400000     # 24小时（毫秒）
      
      # 存储桶配置（新设计）
      buckets:
        # 默认存储桶
        default:
          name: "default"
          storage-path: "./data/buckets/default"
          upload-rate-limit: 10485760   # 10MB/s
          download-rate-limit: 10485760 # 10MB/s
          default-chunk-size: 2097152   # 2MB
          max-file-size: 104857600      # 100MB
          max-in-memory-size: 10485760  # 10MB
          rate-limit-enabled: true
          
        # 文档存储桶
        documents:
          name: "documents"
          storage-path: "./data/buckets/documents"
          upload-rate-limit: 5242880    # 5MB/s
          download-rate-limit: 5242880  # 5MB/s
          default-chunk-size: 1048576   # 1MB
          max-file-size: 52428800       # 50MB
          max-in-memory-size: 5242880   # 5MB
          rate-limit-enabled: true
          
        # 媒体存储桶
        media:
          name: "media"
          storage-path: "./data/buckets/media"
          upload-rate-limit: 20971520   # 20MB/s
          download-rate-limit: 20971520 # 20MB/s
          default-chunk-size: 4194304   # 4MB
          max-file-size: 1073741824     # 1GB
          max-in-memory-size: 20971520  # 20MB
          rate-limit-enabled: true
      
      # 用户配置（权限和个性化设置）
      users:
        # 基础用户 - 只能访问default存储桶
        user1:
          secret-key: "user1-secret-2024"
          allowed-buckets: ["default"]
          upload-rate-limit: 2097152    # 2MB/s 用户级限制
          download-rate-limit: 2097152  # 2MB/s
          max-file-size: 10485760       # 10MB
          
        # 文档用户 - 可以访问default和documents存储桶
        doc-user:
          secret-key: "doc-user-secret-2024"
          allowed-buckets: ["default", "documents"]
          # 使用存储桶默认配置
          
        # VIP用户 - 可以访问所有存储桶
        vip:
          secret-key: "vip-secret-2024"
          allowed-buckets: ["default", "documents", "media"]
          upload-rate-limit: 20971520   # 20MB/s
          download-rate-limit: 20971520 # 20MB/s
          max-file-size: 2147483648     # 2GB
          
        # 管理员 - 可以访问所有存储桶且无限制
        admin:
          secret-key: "admin-secret-2024"
          allowed-buckets: ["default", "documents", "media"]
          rate-limit-enabled: false     # 不限速

# 注意：
# 1. 存储桶在配置文件中定义，用户通过allowed-buckets指定可访问的存储桶
# 2. 用户级别的配置会覆盖存储桶的默认配置
# 3. 如果用户配置中没有指定某项，将使用对应存储桶的配置
# 4. 删除了传统API的所有支持，只保留S3风格的接口
# 5. 物理文件存储路径为: ${bucket.storage-path}/{key} (新的简化存储结构)

# ===== 客户端配置 (application.yml) =====
file:
  transfer:
    client:
      # 服务器连接配置
      server-addr: "your-domain.com"  # 或 IP地址
      server-port: 49011
      
      # 用户认证配置
      user: "xxx"                      # 用户名
      secret-key: "337c7dc2-30fe-4603" # 与服务端对应用户的密钥一致
      
      # 客户端传输配置
      chunk-size: 1048576              # 1MB (建议与服务端用户配置保持一致)
      max-concurrent-transfers: 2
      connect-timeout-seconds: 30
      read-timeout-seconds: 60
      retry-count: 3

---

# ===== Java代码使用示例 =====

# 服务端 - 无需额外代码，SDK自动处理认证

# 客户端 - 必须配置认证信息
# ```java
# ClientConfig config = new ClientConfig();
# config.getAuth().setServerAddr("your-domain.com");
# config.getAuth().setServerPort(49011);
# config.getAuth().setUser("xxx");
# config.getAuth().setSecretKey("337c7dc2-30fe-4603");
# config.setChunkSize(1024 * 1024); // 1MB
# 
# FileTransferClient client = new FileTransferClient(config);
# 
# // 注意：所有认证参数都是必填的，缺少任何一个都会抛出异常
# ```

# ===== 认证机制说明 =====
# 1. 认证方式：HMAC-SHA256签名
# 2. 认证头：X-File-Transfer-User (用户名)、X-File-Transfer-Auth (签名令牌)
# 3. 令牌格式：Base64(timestamp:signature)
# 4. 签名数据：username:timestamp
# 5. 令牌有效期：5分钟
# 6. secretKey：服务端和客户端必须一致

# ===== 安全建议 =====
# 1. secretKey使用强随机字符串，至少16位
# 2. 不同用户使用不同的secretKey
# 3. 定期更换secretKey
# 4. 生产环境使用HTTPS
# 5. 合理设置用户权限和限制 