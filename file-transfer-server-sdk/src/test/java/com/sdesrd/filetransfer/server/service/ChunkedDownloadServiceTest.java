package com.sdesrd.filetransfer.server.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.DownloadChunkRecord;
import com.sdesrd.filetransfer.server.entity.DownloadTask;
import com.sdesrd.filetransfer.server.entity.ObjectStorage;
import com.sdesrd.filetransfer.server.mapper.DownloadChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.DownloadTaskMapper;
import com.sdesrd.filetransfer.server.mapper.ObjectStorageMapper;

/**
 * 分片下载服务测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("分片下载服务测试")
class ChunkedDownloadServiceTest {
    
    @Mock
    private DownloadTaskMapper downloadTaskMapper;
    
    @Mock
    private DownloadChunkRecordMapper downloadChunkRecordMapper;
    
    @Mock
    private ObjectStorageMapper objectStorageMapper;
    
    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private HttpServletResponse response;
    
    @InjectMocks
    private ChunkedDownloadService chunkedDownloadService;
    
    @TempDir
    Path tempDir;
    
    private static final String TEST_BUCKET = "test-bucket";
    private static final String TEST_KEY = "test/file.txt";
    private static final String TEST_USERNAME = "testuser";
    private static final String TEST_CLIENT_IP = "127.0.0.1";
    private static final String TEST_CONTENT = "这是一个测试文件内容，用于验证分片下载功能。";
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建测试文件
        Path testFilePath = tempDir.resolve("test-file.txt");
        Files.write(testFilePath, TEST_CONTENT.getBytes("UTF-8"));
        
        // 模拟存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName(TEST_BUCKET);
        bucketConfig.setStoragePath(tempDir.toString());
        
        lenient().when(properties.isUserAllowedToBucket(TEST_USERNAME, TEST_BUCKET)).thenReturn(true);
        lenient().when(properties.getBucketConfig(TEST_BUCKET)).thenReturn(bucketConfig);
        
        // 模拟对象存储记录
        ObjectStorage objectStorage = new ObjectStorage();
        objectStorage.setId("test-object-id");
        objectStorage.setBucket(TEST_BUCKET);
        objectStorage.setObjectKey(TEST_KEY);
        objectStorage.setFileName("file.txt");
        objectStorage.setFileSize((long) TEST_CONTENT.getBytes("UTF-8").length);
        objectStorage.setUserName(TEST_USERNAME);
        objectStorage.setCreateTime(LocalDateTime.now());
        objectStorage.setUpdateTime(LocalDateTime.now());
        
        lenient().when(objectStorageMapper.findByBucketAndKey(TEST_BUCKET, TEST_KEY)).thenReturn(objectStorage);
        
        // 创建实际的测试文件（模拟物理文件）
        Path physicalFilePath = tempDir.resolve(TEST_KEY);
        Files.createDirectories(physicalFilePath.getParent());
        Files.write(physicalFilePath, TEST_CONTENT.getBytes("UTF-8"));
    }
    
    @Test
    @DisplayName("初始化分片下载 - 成功")
    void testInitChunkedDownload_Success() {
        // Given
        int chunkSize = 10; // 小分块用于测试
        
        // When
        String downloadId = chunkedDownloadService.initChunkedDownload(
                TEST_BUCKET, TEST_KEY, TEST_USERNAME, TEST_CLIENT_IP, chunkSize);
        
        // Then
        assertNotNull(downloadId);
        assertTrue(downloadId.startsWith("dl_"));
        
        // 验证下载任务被创建
        verify(downloadTaskMapper).insert(any(DownloadTask.class));
        
        // 验证分块记录被创建
        verify(downloadChunkRecordMapper, atLeastOnce()).insert(any(DownloadChunkRecord.class));
    }
    
    @Test
    @DisplayName("初始化分片下载 - 用户无权限")
    void testInitChunkedDownload_NoPermission() {
        // Given
        when(properties.isUserAllowedToBucket(TEST_USERNAME, TEST_BUCKET)).thenReturn(false);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            chunkedDownloadService.initChunkedDownload(
                    TEST_BUCKET, TEST_KEY, TEST_USERNAME, TEST_CLIENT_IP, null);
        });
        
        assertEquals("用户无权访问存储桶: " + TEST_BUCKET, exception.getMessage());
    }
    
    @Test
    @DisplayName("初始化分片下载 - 对象不存在")
    void testInitChunkedDownload_ObjectNotFound() {
        // Given
        when(objectStorageMapper.findByBucketAndKey(TEST_BUCKET, TEST_KEY)).thenReturn(null);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            chunkedDownloadService.initChunkedDownload(
                    TEST_BUCKET, TEST_KEY, TEST_USERNAME, TEST_CLIENT_IP, null);
        });
        
        assertEquals("对象不存在: " + TEST_BUCKET + "/" + TEST_KEY, exception.getMessage());
    }
    
    @Test
    @DisplayName("获取下载状态 - 成功")
    void testGetDownloadStatus_Success() {
        // Given
        String downloadId = "test-download-id";
        DownloadTask downloadTask = createTestDownloadTask(downloadId);
        
        when(downloadTaskMapper.selectById(downloadId)).thenReturn(downloadTask);
        
        // When
        DownloadTask result = chunkedDownloadService.getDownloadStatus(downloadId, TEST_USERNAME);
        
        // Then
        assertNotNull(result);
        assertEquals(downloadId, result.getDownloadId());
        assertEquals(TEST_USERNAME, result.getUserName());
    }
    
    @Test
    @DisplayName("获取下载状态 - 任务不存在")
    void testGetDownloadStatus_TaskNotFound() {
        // Given
        String downloadId = "non-existent-id";
        when(downloadTaskMapper.selectById(downloadId)).thenReturn(null);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            chunkedDownloadService.getDownloadStatus(downloadId, TEST_USERNAME);
        });
        
        assertEquals("下载任务不存在或无权限", exception.getMessage());
    }
    
    @Test
    @DisplayName("暂停下载任务 - 成功")
    void testPauseDownload_Success() {
        // Given
        String downloadId = "test-download-id";
        when(downloadTaskMapper.pauseDownload(downloadId, TEST_USERNAME)).thenReturn(1);
        
        // When & Then
        assertDoesNotThrow(() -> {
            chunkedDownloadService.pauseDownload(downloadId, TEST_USERNAME);
        });
        
        verify(downloadTaskMapper).pauseDownload(downloadId, TEST_USERNAME);
    }
    
    @Test
    @DisplayName("暂停下载任务 - 失败")
    void testPauseDownload_Failed() {
        // Given
        String downloadId = "test-download-id";
        when(downloadTaskMapper.pauseDownload(downloadId, TEST_USERNAME)).thenReturn(0);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            chunkedDownloadService.pauseDownload(downloadId, TEST_USERNAME);
        });
        
        assertEquals("无法暂停下载任务，任务不存在或状态不正确", exception.getMessage());
    }
    
    @Test
    @DisplayName("恢复下载任务 - 成功")
    void testResumeDownload_Success() {
        // Given
        String downloadId = "test-download-id";
        when(downloadTaskMapper.resumeDownload(downloadId, TEST_USERNAME)).thenReturn(1);
        
        // When & Then
        assertDoesNotThrow(() -> {
            chunkedDownloadService.resumeDownload(downloadId, TEST_USERNAME);
        });
        
        verify(downloadTaskMapper).resumeDownload(downloadId, TEST_USERNAME);
    }
    
    @Test
    @DisplayName("取消下载任务 - 成功")
    void testCancelDownload_Success() {
        // Given
        String downloadId = "test-download-id";
        when(downloadTaskMapper.cancelDownload(downloadId, TEST_USERNAME)).thenReturn(1);
        
        // When & Then
        assertDoesNotThrow(() -> {
            chunkedDownloadService.cancelDownload(downloadId, TEST_USERNAME);
        });
        
        verify(downloadTaskMapper).cancelDownload(downloadId, TEST_USERNAME);
    }
    
    @Test
    @DisplayName("获取下载分块列表 - 成功")
    void testGetDownloadChunks_Success() {
        // Given
        String downloadId = "test-download-id";
        DownloadTask downloadTask = createTestDownloadTask(downloadId);
        List<DownloadChunkRecord> chunks = Arrays.asList(
                createTestChunkRecord(downloadId, 0),
                createTestChunkRecord(downloadId, 1)
        );
        
        when(downloadTaskMapper.selectById(downloadId)).thenReturn(downloadTask);
        when(downloadChunkRecordMapper.findByDownloadId(downloadId)).thenReturn(chunks);
        
        // When
        List<DownloadChunkRecord> result = chunkedDownloadService.getDownloadChunks(downloadId, TEST_USERNAME);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(0, result.get(0).getChunkIndex());
        assertEquals(1, result.get(1).getChunkIndex());
    }
    
    // === 辅助方法 ===
    
    private DownloadTask createTestDownloadTask(String downloadId) {
        DownloadTask task = new DownloadTask();
        task.setDownloadId(downloadId);
        task.setBucket(TEST_BUCKET);
        task.setObjectKey(TEST_KEY);
        task.setFileName("file.txt");
        task.setFileSize(100L);
        task.setUserName(TEST_USERNAME);
        task.setChunkSize(10);
        task.setTotalChunks(10);
        task.setDownloadedChunks(0);
        task.setStatus(DownloadTask.Status.INITIALIZED);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        return task;
    }
    
    private DownloadChunkRecord createTestChunkRecord(String downloadId, int chunkIndex) {
        DownloadChunkRecord record = new DownloadChunkRecord();
        record.setId("chunk-" + chunkIndex);
        record.setDownloadId(downloadId);
        record.setChunkIndex(chunkIndex);
        record.setChunkSize(10L);
        record.setStartPosition((long) chunkIndex * 10);
        record.setEndPosition((long) chunkIndex * 10 + 9);
        record.setStatus(DownloadChunkRecord.Status.PENDING);
        record.setRetryCount(0);
        record.setMaxRetryCount(3);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        return record;
    }
}
