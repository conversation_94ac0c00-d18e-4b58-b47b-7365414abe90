package com.sdesrd.filetransfer.server.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;

/**
 * S3风格文件服务路径处理测试
 * 专门测试新的存储路径逻辑：${bucket.storage-path}/{key}
 */
@DisplayName("S3风格文件服务路径处理测试")
class S3StyleFileServicePathTest {
    
    // === 常量定义 ===
    
    /**
     * 测试存储桶名称
     */
    private static final String TEST_BUCKET = "test-bucket";
    
    /**
     * 测试用户名
     */
    private static final String TEST_USERNAME = "test-user";
    
    /**
     * 测试对象key
     */
    private static final String TEST_KEY = "documents/reports/2024/annual.pdf";
    
    /**
     * 测试文件内容
     */
    private static final String TEST_FILE_CONTENT = "这是一个测试文件内容，用于验证新的存储路径逻辑。";
    
    // === 测试组件 ===
    
    @TempDir
    Path tempDir;
    
    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private FileTransferProperties.BucketConfig bucketConfig;
    
    private S3StyleFileService fileService;
    
    private String bucketStoragePath;
    
    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试存储路径
        bucketStoragePath = tempDir.resolve("buckets").resolve(TEST_BUCKET).toString();
        Files.createDirectories(Paths.get(bucketStoragePath));
        
        // 配置Mock对象
        when(properties.getBucketConfig(TEST_BUCKET)).thenReturn(bucketConfig);
        when(bucketConfig.getStoragePath()).thenReturn(bucketStoragePath);
        
        // 创建服务实例
        fileService = new S3StyleFileService();
        ReflectionTestUtils.setField(fileService, "properties", properties);
    }
    
    @Test
    @DisplayName("新存储路径格式验证测试")
    void testNewStoragePathFormat() throws Exception {
        // 使用反射调用私有方法进行测试
        String actualPath = (String) ReflectionTestUtils.invokeMethod(
            fileService, "getPhysicalFilePath", TEST_BUCKET, TEST_KEY);
        
        // 验证路径格式：${bucket.storage-path}/{key}
        String expectedPath = bucketStoragePath + "/" + TEST_KEY;
        assertEquals(expectedPath, actualPath);
        
        // 验证路径包含正确的目录结构
        assertTrue(actualPath.contains("documents/reports/2024"));
        assertTrue(actualPath.endsWith("annual.pdf"));
    }
    
    @Test
    @DisplayName("路径安全性验证测试")
    void testPathSecurityValidation() {
        // 测试路径遍历攻击
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, "../../../etc/passwd");
        });
        
        // 测试绝对路径
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, "/etc/passwd");
        });
        
        // 测试Windows驱动器路径
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, "C:\\Windows\\System32");
        });
        
        // 测试危险字符
        String[] dangerousKeys = {
            "file<name>.txt",
            "file>name.txt", 
            "file:name.txt",
            "file\"name.txt",
            "file|name.txt",
            "file?name.txt",
            "file*name.txt"
        };
        
        for (String dangerousKey : dangerousKeys) {
            assertThrows(IllegalArgumentException.class, () -> {
                ReflectionTestUtils.invokeMethod(
                    fileService, "getPhysicalFilePath", TEST_BUCKET, dangerousKey);
            }, "应该拒绝包含危险字符的key: " + dangerousKey);
        }
    }
    
    @Test
    @DisplayName("有效key格式验证测试")
    void testValidKeyFormats() throws Exception {
        // 测试各种有效的key格式
        String[] validKeys = {
            "simple-file.txt",
            "documents/report.pdf",
            "images/2024/photo.jpg",
            "data/users/user123/profile.json",
            "backup/2024-01-15/database.sql",
            "logs/application-2024-01-15.log",
            "files/with-dashes-and_underscores.txt",
            "files/with.dots.in.name.txt"
        };
        
        for (String validKey : validKeys) {
            assertDoesNotThrow(() -> {
                String path = (String) ReflectionTestUtils.invokeMethod(
                    fileService, "getPhysicalFilePath", TEST_BUCKET, validKey);
                assertNotNull(path);
                assertTrue(path.endsWith(validKey));
            }, "应该接受有效的key格式: " + validKey);
        }
    }
    
    @Test
    @DisplayName("存储桶不存在异常测试")
    void testBucketNotExistsException() {
        // 配置不存在的存储桶
        when(properties.getBucketConfig("non-existent-bucket")).thenReturn(null);
        
        // 验证异常抛出
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", "non-existent-bucket", TEST_KEY);
        });
    }
    
    @Test
    @DisplayName("空key验证测试")
    void testEmptyKeyValidation() {
        // 测试空key
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, "");
        });
        
        // 测试null key
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, (String) null);
        });
        
        // 测试只包含空格的key
        assertThrows(IllegalArgumentException.class, () -> {
            ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, "   ");
        });
    }
    
    @Test
    @DisplayName("路径构建性能测试")
    void testPathBuildingPerformance() throws Exception {
        System.out.println("\n=== 测试：路径构建性能 ===");

        // 测试大量路径构建的性能
        long startTime = System.currentTimeMillis();

        final int TEST_ITERATIONS = 10000;
        for (int i = 0; i < TEST_ITERATIONS; i++) {
            String key = "performance/test/file" + i + ".txt";
            String path = (String) ReflectionTestUtils.invokeMethod(
                fileService, "getPhysicalFilePath", TEST_BUCKET, key);
            assertNotNull(path);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("[性能测试] 执行" + TEST_ITERATIONS + "次路径构建");
        System.out.println("  实际耗时: " + duration + "ms");
        System.out.println("  平均耗时: " + (duration / (double) TEST_ITERATIONS) + "ms/次");

        // 验证性能：10000次路径构建应该在5秒内完成（适应测试环境性能限制）
        final int PERFORMANCE_LIMIT_MS = 5000;
        assertTrue(duration < PERFORMANCE_LIMIT_MS,
            "路径构建性能测试失败，耗时: " + duration + "ms，超过限制: " + PERFORMANCE_LIMIT_MS + "ms");

        System.out.println("✅ 路径构建性能测试通过");
    }
    
    @Test
    @DisplayName("中文路径支持测试")
    void testChinesePathSupport() throws Exception {
        // 测试中文文件名和路径
        String chineseKey = "文档/报告/2024年度/年度总结.pdf";
        
        String path = (String) ReflectionTestUtils.invokeMethod(
            fileService, "getPhysicalFilePath", TEST_BUCKET, chineseKey);
        
        assertNotNull(path);
        assertTrue(path.contains("文档/报告/2024年度"));
        assertTrue(path.endsWith("年度总结.pdf"));
    }
    
    @Test
    @DisplayName("长路径支持测试")
    void testLongPathSupport() throws Exception {
        // 构建一个很长的key
        StringBuilder longKeyBuilder = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            longKeyBuilder.append("very-long-directory-name-").append(i).append("/");
        }
        longKeyBuilder.append("very-long-file-name-with-many-characters.txt");
        
        String longKey = longKeyBuilder.toString();
        
        String path = (String) ReflectionTestUtils.invokeMethod(
            fileService, "getPhysicalFilePath", TEST_BUCKET, longKey);
        
        assertNotNull(path);
        assertTrue(path.endsWith(longKey));
    }
}
