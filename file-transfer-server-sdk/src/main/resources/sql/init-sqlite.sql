-- 文件传输系统数据库初始化脚本
-- 版本: 5.1.0 (重新添加MD5字段用于传输完整性校验)
-- 创建时间: 2024-01-15
-- 更新时间: 2025-06-19

-- 系统版本表
CREATE TABLE IF NOT EXISTS system_version (
    id INTEGER PRIMARY KEY,
    version TEXT NOT NULL,
    update_time TIMESTAMP NOT NULL
);

-- 插入或更新版本信息
INSERT OR REPLACE INTO system_version (id, version, update_time) 
VALUES (1, '5.1.0', strftime('%Y-%m-%d %H:%M:%f', 'now'));

-- 对象存储映射表（简化设计，删除MD5字段）
CREATE TABLE IF NOT EXISTS object_storage (
    id TEXT PRIMARY KEY,              -- 唯一ID
    bucket TEXT NOT NULL,             -- 存储桶名称
    object_key TEXT NOT NULL,         -- 对象key
    file_id TEXT NOT NULL,            -- 物理文件ID
    file_name TEXT NOT NULL,          -- 原始文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    content_type TEXT,                -- 文件类型
    user_name TEXT NOT NULL,          -- 创建用户
    create_time TIMESTAMP NOT NULL,   -- 创建时间 (TIMESTAMP类型)
    update_time TIMESTAMP NOT NULL    -- 更新时间 (TIMESTAMP类型)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_object_bucket_key ON object_storage(bucket, object_key);
CREATE INDEX IF NOT EXISTS idx_object_user ON object_storage(user_name);
CREATE INDEX IF NOT EXISTS idx_object_file_id ON object_storage(file_id);

-- 确保同一存储桶中的key唯一
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_bucket_key ON object_storage(bucket, object_key);

-- 传输任务表（用于跟踪上传进度，包含MD5校验字段）
CREATE TABLE IF NOT EXISTS transfer_task (
    transfer_id TEXT PRIMARY KEY,     -- 传输ID
    bucket TEXT NOT NULL,             -- 存储桶
    object_key TEXT NOT NULL,         -- 对象key
    file_name TEXT NOT NULL,          -- 文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    file_md5 TEXT,                    -- 文件MD5（用于传输完整性校验）
    user_name TEXT NOT NULL,          -- 用户名
    chunk_size INTEGER NOT NULL,      -- 分块大小
    total_chunks INTEGER NOT NULL,    -- 总分块数
    completed_chunks INTEGER DEFAULT 0, -- 已完成分块数
    status INTEGER DEFAULT 0,         -- 状态：0-初始化，1-上传中，2-完成，3-失败
    create_time TIMESTAMP NOT NULL,   -- 创建时间 (TIMESTAMP类型)
    update_time TIMESTAMP NOT NULL,   -- 更新时间 (TIMESTAMP类型)
    complete_time TIMESTAMP           -- 完成时间 (TIMESTAMP类型)
);

-- 创建传输任务索引
CREATE INDEX IF NOT EXISTS idx_transfer_user ON transfer_task(user_name);
CREATE INDEX IF NOT EXISTS idx_transfer_status ON transfer_task(status);
CREATE INDEX IF NOT EXISTS idx_transfer_bucket_key ON transfer_task(bucket, object_key);

-- 分块记录表（删除MD5字段）
CREATE TABLE IF NOT EXISTS chunk_record (
    id TEXT PRIMARY KEY,              -- 唯一ID
    transfer_id TEXT NOT NULL,        -- 传输ID
    chunk_index INTEGER NOT NULL,     -- 分块索引
    chunk_size INTEGER NOT NULL,      -- 分块大小
    chunk_path TEXT NOT NULL,         -- 分块临时路径
    status INTEGER DEFAULT 0,         -- 状态：0-待上传，1-已上传
    create_time TIMESTAMP NOT NULL,   -- 创建时间 (TIMESTAMP类型)
    FOREIGN KEY (transfer_id) REFERENCES transfer_task(transfer_id)
);

-- 创建分块索引
CREATE INDEX IF NOT EXISTS idx_chunk_transfer ON chunk_record(transfer_id);
CREATE INDEX IF NOT EXISTS idx_chunk_status ON chunk_record(status);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_transfer_chunk ON chunk_record(transfer_id, chunk_index);

-- 下载任务表（用于支持断点续传的分片下载）
CREATE TABLE IF NOT EXISTS download_task (
    download_id TEXT PRIMARY KEY,         -- 下载任务ID
    bucket TEXT NOT NULL,                 -- 存储桶名称
    object_key TEXT NOT NULL,             -- 对象key
    file_name TEXT NOT NULL,              -- 文件名
    file_size INTEGER NOT NULL,           -- 文件总大小
    file_md5 TEXT,                        -- 文件MD5（用于完整性校验）
    user_name TEXT NOT NULL,              -- 用户名
    chunk_size INTEGER NOT NULL,          -- 分块大小
    total_chunks INTEGER NOT NULL,        -- 总分块数
    downloaded_chunks INTEGER DEFAULT 0,  -- 已下载分块数
    status INTEGER DEFAULT 0,             -- 状态：0-初始化，1-下载中，2-完成，3-失败，4-暂停
    client_ip TEXT,                       -- 客户端IP地址
    create_time TIMESTAMP NOT NULL,       -- 创建时间
    update_time TIMESTAMP NOT NULL,       -- 更新时间
    complete_time TIMESTAMP               -- 完成时间
);

-- 创建下载任务索引
CREATE INDEX IF NOT EXISTS idx_download_user ON download_task(user_name);
CREATE INDEX IF NOT EXISTS idx_download_status ON download_task(status);
CREATE INDEX IF NOT EXISTS idx_download_bucket_key ON download_task(bucket, object_key);

-- 下载分块记录表（用于跟踪分片下载的每个分块状态）
CREATE TABLE IF NOT EXISTS download_chunk_record (
    id TEXT PRIMARY KEY,                  -- 记录ID
    download_id TEXT NOT NULL,            -- 下载任务ID
    chunk_index INTEGER NOT NULL,         -- 分块索引（从0开始）
    chunk_size INTEGER NOT NULL,          -- 分块大小
    start_position INTEGER NOT NULL,      -- 分块在文件中的起始位置
    end_position INTEGER NOT NULL,        -- 分块在文件中的结束位置
    status INTEGER DEFAULT 0,             -- 状态：0-待下载，1-下载中，2-已下载，3-下载失败
    retry_count INTEGER DEFAULT 0,        -- 重试次数
    max_retry_count INTEGER DEFAULT 3,    -- 最大重试次数
    error_message TEXT,                   -- 错误信息（如果下载失败）
    create_time TIMESTAMP NOT NULL,       -- 创建时间
    update_time TIMESTAMP NOT NULL,       -- 更新时间
    download_time TIMESTAMP,              -- 下载完成时间
    FOREIGN KEY (download_id) REFERENCES download_task(download_id)
);

-- 创建下载分块记录索引
CREATE INDEX IF NOT EXISTS idx_download_chunk_task ON download_chunk_record(download_id);
CREATE INDEX IF NOT EXISTS idx_download_chunk_status ON download_chunk_record(status);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_download_chunk ON download_chunk_record(download_id, chunk_index);