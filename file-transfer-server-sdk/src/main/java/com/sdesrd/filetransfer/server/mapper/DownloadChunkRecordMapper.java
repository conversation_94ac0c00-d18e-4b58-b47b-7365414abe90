package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.DownloadChunkRecord;

/**
 * 下载分块记录Mapper接口
 * 提供下载分块记录的数据库操作方法
 */
@Mapper
public interface DownloadChunkRecordMapper extends BaseMapper<DownloadChunkRecord> {
    
    /**
     * 根据下载任务ID查询所有分块记录
     * 
     * @param downloadId 下载任务ID
     * @return 分块记录列表，按分块索引排序
     */
    @Select("SELECT * FROM download_chunk_record WHERE download_id = #{downloadId} ORDER BY chunk_index")
    List<DownloadChunkRecord> findByDownloadId(@Param("downloadId") String downloadId);
    
    /**
     * 查询特定分块记录
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @return 分块记录
     */
    @Select("SELECT * FROM download_chunk_record WHERE download_id = #{downloadId} AND chunk_index = #{chunkIndex}")
    DownloadChunkRecord findByDownloadIdAndIndex(@Param("downloadId") String downloadId, @Param("chunkIndex") Integer chunkIndex);
    
    /**
     * 更新分块状态
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @param status 新状态
     * @return 更新的记录数
     */
    @Update("UPDATE download_chunk_record SET status = #{status}, update_time = datetime('now') WHERE download_id = #{downloadId} AND chunk_index = #{chunkIndex}")
    int updateChunkStatus(@Param("downloadId") String downloadId, 
                         @Param("chunkIndex") Integer chunkIndex, 
                         @Param("status") Integer status);
    
    /**
     * 更新分块状态和下载时间
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @param status 新状态
     * @return 更新的记录数
     */
    @Update("UPDATE download_chunk_record SET status = #{status}, download_time = datetime('now'), update_time = datetime('now') WHERE download_id = #{downloadId} AND chunk_index = #{chunkIndex}")
    int updateChunkStatusWithDownloadTime(@Param("downloadId") String downloadId, 
                                         @Param("chunkIndex") Integer chunkIndex, 
                                         @Param("status") Integer status);
    
    /**
     * 更新分块重试信息
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @param retryCount 重试次数
     * @param errorMessage 错误信息
     * @return 更新的记录数
     */
    @Update("UPDATE download_chunk_record SET retry_count = #{retryCount}, error_message = #{errorMessage}, update_time = datetime('now') WHERE download_id = #{downloadId} AND chunk_index = #{chunkIndex}")
    int updateChunkRetryInfo(@Param("downloadId") String downloadId, 
                            @Param("chunkIndex") Integer chunkIndex, 
                            @Param("retryCount") Integer retryCount, 
                            @Param("errorMessage") String errorMessage);
    
    /**
     * 统计已下载的分块数
     * 
     * @param downloadId 下载任务ID
     * @return 已下载的分块数
     */
    @Select("SELECT COUNT(*) FROM download_chunk_record WHERE download_id = #{downloadId} AND status = 2")
    int countDownloadedChunks(@Param("downloadId") String downloadId);
    
    /**
     * 统计失败的分块数
     * 
     * @param downloadId 下载任务ID
     * @return 失败的分块数
     */
    @Select("SELECT COUNT(*) FROM download_chunk_record WHERE download_id = #{downloadId} AND status = 3")
    int countFailedChunks(@Param("downloadId") String downloadId);
    
    /**
     * 查询待下载的分块
     * 
     * @param downloadId 下载任务ID
     * @return 待下载的分块列表
     */
    @Select("SELECT * FROM download_chunk_record WHERE download_id = #{downloadId} AND status = 0 ORDER BY chunk_index")
    List<DownloadChunkRecord> findPendingChunks(@Param("downloadId") String downloadId);
    
    /**
     * 查询可重试的失败分块
     * 
     * @param downloadId 下载任务ID
     * @return 可重试的失败分块列表
     */
    @Select("SELECT * FROM download_chunk_record WHERE download_id = #{downloadId} AND status = 3 AND (max_retry_count IS NULL OR retry_count < max_retry_count) ORDER BY chunk_index")
    List<DownloadChunkRecord> findRetryableFailedChunks(@Param("downloadId") String downloadId);
    
    /**
     * 查询正在下载的分块
     * 
     * @param downloadId 下载任务ID
     * @return 正在下载的分块列表
     */
    @Select("SELECT * FROM download_chunk_record WHERE download_id = #{downloadId} AND status = 1 ORDER BY chunk_index")
    List<DownloadChunkRecord> findDownloadingChunks(@Param("downloadId") String downloadId);
    
    /**
     * 删除下载任务的所有分块记录
     * 
     * @param downloadId 下载任务ID
     * @return 删除的记录数
     */
    @Update("DELETE FROM download_chunk_record WHERE download_id = #{downloadId}")
    int deleteByDownloadId(@Param("downloadId") String downloadId);
    
    /**
     * 重置分块状态（用于重新开始下载）
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @return 更新的记录数
     */
    @Update("UPDATE download_chunk_record SET status = 0, retry_count = 0, error_message = NULL, download_time = NULL, update_time = datetime('now') WHERE download_id = #{downloadId} AND chunk_index = #{chunkIndex}")
    int resetChunkStatus(@Param("downloadId") String downloadId, @Param("chunkIndex") Integer chunkIndex);
    
    /**
     * 批量重置所有分块状态
     * 
     * @param downloadId 下载任务ID
     * @return 更新的记录数
     */
    @Update("UPDATE download_chunk_record SET status = 0, retry_count = 0, error_message = NULL, download_time = NULL, update_time = datetime('now') WHERE download_id = #{downloadId}")
    int resetAllChunkStatus(@Param("downloadId") String downloadId);
}
