package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 分块记录实体
 */
@Data
@TableName("chunk_record")
public class ChunkRecord {
    
    /**
     * 唯一ID
     */
    @TableId
    private String id;
    
    /**
     * 传输ID
     */
    private String transferId;
    
    /**
     * 分块索引
     */
    private Integer chunkIndex;
    
    /**
     * 分块大小
     */
    private Integer chunkSize;
    
    /**
     * 分块临时路径
     */
    private String chunkPath;
    
    /**
     * 状态：0-待上传，1-已上传
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 状态枚举
     */
    public static class Status {
        public static final int PENDING = 0;
        public static final int UPLOADED = 1;
    }
    
    /**
     * 生成唯一ID
     */
    public static String generateId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }
} 