package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.DownloadTask;

/**
 * 下载任务Mapper接口
 * 提供下载任务的数据库操作方法
 */
@Mapper
public interface DownloadTaskMapper extends BaseMapper<DownloadTask> {
    
    /**
     * 更新下载进度
     * 
     * @param downloadId 下载任务ID
     * @param downloadedChunks 已下载分块数
     * @param status 下载状态
     * @return 更新的记录数
     */
    @Update("UPDATE download_task SET downloaded_chunks = #{downloadedChunks}, status = #{status}, update_time = datetime('now') WHERE download_id = #{downloadId}")
    int updateProgress(@Param("downloadId") String downloadId, 
                      @Param("downloadedChunks") Integer downloadedChunks, 
                      @Param("status") Integer status);
    
    /**
     * 完成下载任务
     * 
     * @param downloadId 下载任务ID
     * @param status 最终状态
     * @return 更新的记录数
     */
    @Update("UPDATE download_task SET status = #{status}, complete_time = datetime('now'), update_time = datetime('now') WHERE download_id = #{downloadId}")
    int completeDownload(@Param("downloadId") String downloadId, @Param("status") Integer status);
    
    /**
     * 根据状态查询下载任务
     * 
     * @param status 下载状态
     * @return 下载任务列表
     */
    @Select("SELECT * FROM download_task WHERE status = #{status}")
    List<DownloadTask> findByStatus(@Param("status") Integer status);
    
    /**
     * 查询用户的下载任务
     * 
     * @param userName 用户名
     * @return 下载任务列表
     */
    @Select("SELECT * FROM download_task WHERE user_name = #{userName} ORDER BY create_time DESC")
    List<DownloadTask> findByUser(@Param("userName") String userName);
    
    /**
     * 根据存储桶和对象key查询下载任务
     * 
     * @param bucket 存储桶名称
     * @param objectKey 对象key
     * @param userName 用户名
     * @return 下载任务列表
     */
    @Select("SELECT * FROM download_task WHERE bucket = #{bucket} AND object_key = #{objectKey} AND user_name = #{userName} ORDER BY create_time DESC")
    List<DownloadTask> findByBucketAndKey(@Param("bucket") String bucket, 
                                         @Param("objectKey") String objectKey, 
                                         @Param("userName") String userName);
    
    /**
     * 查询用户正在进行的下载任务
     * 
     * @param userName 用户名
     * @return 正在进行的下载任务列表
     */
    @Select("SELECT * FROM download_task WHERE user_name = #{userName} AND status IN (0, 1, 4) ORDER BY create_time DESC")
    List<DownloadTask> findActiveDownloadsByUser(@Param("userName") String userName);
    
    /**
     * 清理过期的下载任务
     * 删除24小时前创建的已完成或失败的下载任务
     * 
     * @return 清理的记录数
     */
    @Update("DELETE FROM download_task WHERE status IN (2, 3) AND create_time < datetime('now', '-24 hours')")
    int cleanupExpiredTasks();
    
    /**
     * 暂停下载任务
     * 
     * @param downloadId 下载任务ID
     * @param userName 用户名（用于权限验证）
     * @return 更新的记录数
     */
    @Update("UPDATE download_task SET status = 4, update_time = datetime('now') WHERE download_id = #{downloadId} AND user_name = #{userName} AND status IN (0, 1)")
    int pauseDownload(@Param("downloadId") String downloadId, @Param("userName") String userName);
    
    /**
     * 恢复下载任务
     * 
     * @param downloadId 下载任务ID
     * @param userName 用户名（用于权限验证）
     * @return 更新的记录数
     */
    @Update("UPDATE download_task SET status = 1, update_time = datetime('now') WHERE download_id = #{downloadId} AND user_name = #{userName} AND status = 4")
    int resumeDownload(@Param("downloadId") String downloadId, @Param("userName") String userName);
    
    /**
     * 取消下载任务
     * 
     * @param downloadId 下载任务ID
     * @param userName 用户名（用于权限验证）
     * @return 更新的记录数
     */
    @Update("UPDATE download_task SET status = 3, complete_time = datetime('now'), update_time = datetime('now') WHERE download_id = #{downloadId} AND user_name = #{userName} AND status IN (0, 1, 4)")
    int cancelDownload(@Param("downloadId") String downloadId, @Param("userName") String userName);
    
    /**
     * 统计用户的下载任务数量
     * 
     * @param userName 用户名
     * @param status 状态（可选，为null时统计所有状态）
     * @return 任务数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM download_task WHERE user_name = #{userName}" +
            "<if test='status != null'> AND status = #{status}</if>" +
            "</script>")
    int countByUserAndStatus(@Param("userName") String userName, @Param("status") Integer status);
}
