package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 下载任务实体
 * 用于支持断点续传的分片下载功能
 */
@Data
@TableName("download_task")
public class DownloadTask {
    
    /**
     * 下载任务ID
     */
    @TableId
    private String downloadId;
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 对象key
     */
    private String objectKey;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件总大小
     */
    private Long fileSize;
    
    /**
     * 文件MD5（用于完整性校验）
     */
    private String fileMd5;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 分块大小
     */
    private Integer chunkSize;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已下载分块数
     */
    private Integer downloadedChunks;
    
    /**
     * 下载状态：0-初始化，1-下载中，2-完成，3-失败，4-暂停
     */
    private Integer status;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 下载状态常量
     */
    public static class Status {
        /** 初始化状态 */
        public static final int INITIALIZED = 0;
        /** 下载中状态 */
        public static final int DOWNLOADING = 1;
        /** 完成状态 */
        public static final int COMPLETED = 2;
        /** 失败状态 */
        public static final int FAILED = 3;
        /** 暂停状态 */
        public static final int PAUSED = 4;
    }
    
    /**
     * 生成下载任务ID
     * 
     * @return 唯一的下载任务ID
     */
    public static String generateDownloadId() {
        return "dl_" + java.util.UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 计算下载进度百分比
     * 
     * @return 下载进度（0-100）
     */
    public double getProgressPercentage() {
        if (totalChunks == null || totalChunks == 0) {
            return 0.0;
        }
        if (downloadedChunks == null) {
            return 0.0;
        }
        return (double) downloadedChunks / totalChunks * 100.0;
    }
    
    /**
     * 检查下载是否完成
     * 
     * @return 是否完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED == status;
    }
    
    /**
     * 检查下载是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return Status.FAILED == status;
    }
    
    /**
     * 检查下载是否暂停
     * 
     * @return 是否暂停
     */
    public boolean isPaused() {
        return Status.PAUSED == status;
    }
}
