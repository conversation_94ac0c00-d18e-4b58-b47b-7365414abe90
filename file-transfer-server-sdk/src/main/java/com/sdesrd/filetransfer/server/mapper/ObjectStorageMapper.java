package com.sdesrd.filetransfer.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.ObjectStorage;

/**
 * 对象存储Mapper
 */
@Mapper
public interface ObjectStorageMapper extends BaseMapper<ObjectStorage> {
    
    /**
     * 根据存储桶和对象key查找对象
     */
    @Select("SELECT * FROM object_storage WHERE bucket = #{bucket} AND object_key = #{objectKey}")
    ObjectStorage findByBucketAndKey(@Param("bucket") String bucket, @Param("objectKey") String objectKey);
    
    /**
     * 列出存储桶中指定前缀的对象
     */
    @Select("SELECT * FROM object_storage WHERE bucket = #{bucket} AND object_key LIKE #{prefix} ORDER BY object_key")
    List<ObjectStorage> listObjectsByPrefix(@Param("bucket") String bucket, @Param("prefix") String prefix);
    
    /**
     * 统计存储桶中的对象数量
     */
    @Select("SELECT COUNT(*) FROM object_storage WHERE bucket = #{bucket}")
    int countByBucket(@Param("bucket") String bucket);
    
    /**
     * 统计用户的对象数量
     */
    @Select("SELECT COUNT(*) FROM object_storage WHERE bucket = #{bucket} AND user_name = #{userName}")
    int countByBucketAndUser(@Param("bucket") String bucket, @Param("userName") String userName);
} 