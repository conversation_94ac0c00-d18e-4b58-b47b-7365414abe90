package com.sdesrd.filetransfer.server.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.DeleteObjectsRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.S3ListObjectsResponse;
import com.sdesrd.filetransfer.server.dto.S3ObjectInfo;
import com.sdesrd.filetransfer.server.dto.S3PutObjectRequest;
import com.sdesrd.filetransfer.server.dto.TransferProgressResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.S3StyleFileService;

import lombok.extern.slf4j.Slf4j;

/**
 * S3风格文件传输控制器
 * 提供类似AWS S3的REST API接口，支持存储桶概念
 */
@Slf4j
@RestController
@RequestMapping("/filetransfer/api/s3")
public class S3StyleFileController {
    
    @Autowired
    private S3StyleFileService s3StyleFileService;
    
    /**
     * 初始化对象上传
     * POST /filetransfer/api/s3/upload/init
     */
    @PostMapping("/upload/init")
    public ApiResult<FileUploadInitResponse> initUpload(
            @RequestBody S3PutObjectRequest request,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("初始化上传 - 用户: {}, 存储桶: {}, key: {}, 文件: {}, IP: {}", 
                    username, request.getBucket(), request.getKey(), request.getFileName(), clientIp);
            
            FileUploadInitResponse response = s3StyleFileService.initUpload(request, username, clientIp);
            return ApiResult.success(response);
            
        } catch (Exception e) {
            log.error("初始化上传失败", e);
            return ApiResult.error(500, e.getMessage());
        }
    }
    
    /**
     * 上传文件分块
     * POST /filetransfer/api/s3/upload/chunk
     */
    @PostMapping("/upload/chunk")
    public ApiResult<String> uploadChunk(
            @RequestParam("transferId") String transferId,
            @RequestParam("chunkIndex") Integer chunkIndex,
            @RequestParam("chunk") MultipartFile chunk,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            log.debug("上传分块 - 用户: {}, 传输ID: {}, 分块: {}, 大小: {}", 
                    username, transferId, chunkIndex, chunk.getSize());
            
            s3StyleFileService.uploadChunk(transferId, chunkIndex, chunk, username);
            return ApiResult.success("分块上传成功");
            
        } catch (Exception e) {
            log.error("上传分块失败 - 传输ID: {}, 分块: {}", transferId, chunkIndex, e);
            return ApiResult.error(500, e.getMessage());
        }
    }
    
    /**
     * 完成对象上传
     * POST /filetransfer/api/s3/upload/complete
     */
    @PostMapping("/upload/complete")
    public ApiResult<Map<String, Object>> completeUpload(
            @RequestParam("transferId") String transferId,
            @RequestParam("bucket") String bucket,
            @RequestParam("key") String key,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            log.info("完成上传 - 用户: {}, 传输ID: {}, 存储桶: {}, key: {}", 
                    username, transferId, bucket, key);
            
            Map<String, Object> result = s3StyleFileService.completeUpload(transferId, bucket, key, username);
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("完成上传失败 - 传输ID: {}, key: {}", transferId, key, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 获取对象详细信息（JSON格式）
     * GET /filetransfer/api/s3/info/{bucket}/{key}
     * 注意：此方法必须放在通用的/{bucket}/**映射之前，以避免路径冲突
     */
    @GetMapping("/info/{bucket}/{key:.*}")
    public ApiResult<S3ObjectInfo> getObjectInfo(
            @PathVariable String bucket,
            @PathVariable String key,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            return ApiResult.success(objectInfo);
        } catch (Exception e) {
            log.error("获取对象信息失败: bucket={}, key={}", bucket, key, e);
            return ApiResult.error(404, "对象不存在");
        }
    }

    /**
     * 下载对象
     * GET /filetransfer/api/s3/{bucket}/**
     */
    @GetMapping("/{bucket}/**")
    public ResponseEntity<Resource> getObject(
            @PathVariable String bucket,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            // 从请求URI中提取key
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            
            log.info("下载对象请求 - 用户: {}, 存储桶: {}, key: {}, 请求URI: {}", 
                    username, bucket, key, requestURI);
            
            Resource resource = s3StyleFileService.getObject(bucket, key, username);
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + objectInfo.getFileName() + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, objectInfo.getContentType())
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(objectInfo.getSize()))
                    .header("ETag", objectInfo.getEtag())
                    .body(resource);
        } catch (Exception e) {
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            log.error("下载对象失败: bucket={}, key={}", bucket, key, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * 获取对象元数据
     * HEAD /filetransfer/api/s3/{bucket}/**
     */
    @RequestMapping(value = "/{bucket}/**", method = RequestMethod.HEAD)
    public ResponseEntity<Void> headObject(
            @PathVariable String bucket,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            // 从请求URI中提取key
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            
            S3ObjectInfo objectInfo = s3StyleFileService.getObjectInfo(bucket, key, username);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, objectInfo.getContentType())
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(objectInfo.getSize()))
                    .header("ETag", objectInfo.getEtag())
                    .header("Last-Modified", objectInfo.getLastModified().toString())
                    .build();
        } catch (Exception e) {
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            log.error("获取对象元数据失败: bucket={}, key={}", bucket, key, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    /**
     * 列出存储桶中的对象
     * GET /filetransfer/api/s3/
     */
    @GetMapping("/")
    public ApiResult<S3ListObjectsResponse> listObjects(
            @RequestParam String bucket,
            @RequestParam(required = false) String prefix,
            @RequestParam(required = false) String delimiter,
            @RequestParam(defaultValue = "1000") int maxKeys,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            S3ListObjectsResponse response = s3StyleFileService.listObjects(bucket, prefix, delimiter, maxKeys, username);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("列出对象失败: bucket={}", bucket, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 删除对象
     * DELETE /filetransfer/api/s3/{bucket}/**
     */
    @DeleteMapping("/{bucket}/**")
    public ApiResult<String> deleteObject(
            @PathVariable String bucket,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            // 从请求URI中提取key
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            
            s3StyleFileService.deleteObject(bucket, key, username);
            return ApiResult.success("删除成功");
        } catch (Exception e) {
            String requestURI = httpRequest.getRequestURI();
            String pathPrefix = "/filetransfer/api/s3/" + bucket + "/";
            String key = requestURI.substring(requestURI.indexOf(pathPrefix) + pathPrefix.length());
            log.error("删除对象失败: bucket={}, key={}", bucket, key, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 批量删除对象
     * POST /filetransfer/api/s3/delete
     */
    @PostMapping("/delete")
    public ApiResult<Map<String, Object>> deleteObjects(
            @RequestBody DeleteObjectsRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            Map<String, Object> result = s3StyleFileService.deleteObjects(request, username);
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("批量删除对象失败", e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 查询传输进度
     * GET /filetransfer/api/s3/progress/{transferId}
     */
    @GetMapping("/progress/{transferId}")
    public ApiResult<TransferProgressResponse> getProgress(
            @PathVariable String transferId,
            HttpServletRequest httpRequest) {
        
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            
            TransferProgressResponse progress = s3StyleFileService.getTransferProgress(transferId, username);
            return ApiResult.success(progress);
        } catch (Exception e) {
            log.error("查询传输进度失败: transferId={}", transferId, e);
            return ApiResult.error(404, "传输记录不存在");
        }
    }

    /**
     * 初始化分片下载
     * POST /filetransfer/api/s3/download/init
     */
    @PostMapping("/download/init")
    public ApiResult<Map<String, Object>> initChunkedDownload(
            @RequestParam("bucket") String bucket,
            @RequestParam("key") String key,
            @RequestParam(value = "chunkSize", required = false) Integer chunkSize,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);

            log.info("初始化分片下载 - 用户: {}, 存储桶: {}, key: {}, IP: {}",
                    username, bucket, key, clientIp);

            String downloadId = s3StyleFileService.initChunkedDownload(bucket, key, username, clientIp, chunkSize);

            Map<String, Object> result = new HashMap<>();
            result.put("downloadId", downloadId);
            result.put("bucket", bucket);
            result.put("key", key);

            return ApiResult.success(result);

        } catch (Exception e) {
            log.error("初始化分片下载失败 - bucket: {}, key: {}", bucket, key, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 下载指定分块
     * GET /filetransfer/api/s3/download/chunk
     */
    @GetMapping("/download/chunk")
    public void downloadChunk(
            @RequestParam("downloadId") String downloadId,
            @RequestParam("chunkIndex") Integer chunkIndex,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);

            log.debug("下载分块 - 用户: {}, 下载ID: {}, 分块: {}",
                    username, downloadId, chunkIndex);

            s3StyleFileService.downloadChunk(downloadId, chunkIndex, username, httpResponse);

        } catch (Exception e) {
            log.error("下载分块失败 - 下载ID: {}, 分块: {}", downloadId, chunkIndex, e);
            try {
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                httpResponse.setContentType("application/json;charset=UTF-8");
                httpResponse.getWriter().write("{\"code\":500,\"message\":\"下载分块失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (Exception ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 获取下载任务状态
     * GET /filetransfer/api/s3/download/status/{downloadId}
     */
    @GetMapping("/download/status/{downloadId}")
    public ApiResult<Map<String, Object>> getDownloadStatus(
            @PathVariable String downloadId,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);

            Map<String, Object> status = s3StyleFileService.getDownloadStatus(downloadId, username);
            return ApiResult.success(status);

        } catch (Exception e) {
            log.error("获取下载状态失败 - downloadId: {}", downloadId, e);
            return ApiResult.error(404, "下载任务不存在");
        }
    }

    /**
     * 暂停下载任务
     * POST /filetransfer/api/s3/download/pause/{downloadId}
     */
    @PostMapping("/download/pause/{downloadId}")
    public ApiResult<String> pauseDownload(
            @PathVariable String downloadId,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);

            s3StyleFileService.pauseDownload(downloadId, username);
            return ApiResult.success("下载任务已暂停");

        } catch (Exception e) {
            log.error("暂停下载失败 - downloadId: {}", downloadId, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 恢复下载任务
     * POST /filetransfer/api/s3/download/resume/{downloadId}
     */
    @PostMapping("/download/resume/{downloadId}")
    public ApiResult<String> resumeDownload(
            @PathVariable String downloadId,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);

            s3StyleFileService.resumeDownload(downloadId, username);
            return ApiResult.success("下载任务已恢复");

        } catch (Exception e) {
            log.error("恢复下载失败 - downloadId: {}", downloadId, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 取消下载任务
     * POST /filetransfer/api/s3/download/cancel/{downloadId}
     */
    @PostMapping("/download/cancel/{downloadId}")
    public ApiResult<String> cancelDownload(
            @PathVariable String downloadId,
            HttpServletRequest httpRequest) {
        try {
            String username = AuthInterceptor.getCurrentUser(httpRequest);

            s3StyleFileService.cancelDownload(downloadId, username);
            return ApiResult.success("下载任务已取消");

        } catch (Exception e) {
            log.error("取消下载失败 - downloadId: {}", downloadId, e);
            return ApiResult.error(500, e.getMessage());
        }
    }

    /**
     * 服务健康检查
     * GET /filetransfer/api/s3/health
     */
    @GetMapping("/health")
    public ApiResult<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "S3 File Transfer Service");
        health.put("timestamp", System.currentTimeMillis());
        return ApiResult.success(health);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
} 