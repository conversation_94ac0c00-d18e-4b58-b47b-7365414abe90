package com.sdesrd.filetransfer.server.config;

import java.io.File;

import javax.annotation.PostConstruct;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输服务端自动配置类
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(FileTransferProperties.class)
@ComponentScan(basePackages = "com.sdesrd.filetransfer.server")
@MapperScan(basePackages = "com.sdesrd.filetransfer.server.mapper")
@ConditionalOnProperty(prefix = "file.transfer.server", name = "enabled", havingValue = "true", matchIfMissing = true)
public class FileTransferAutoConfiguration {
    
    @Autowired
    private FileTransferProperties properties;
    
    /**
     * 在配置类初始化时就创建必要的目录
     * 确保在数据源Bean创建之前目录已存在
     */
    @PostConstruct
    public void initDirectories() {
        try {
            // 验证配置的完整性
            log.info("开始验证文件传输服务配置...");
            properties.validateConfiguration();
            log.info("配置验证通过");

            // 创建数据库目录
            if (properties.getDatabasePath() != null) {
                File dbFile = new File(properties.getDatabasePath());
                File dbDir = dbFile.getParentFile();
                if (dbDir != null && !dbDir.exists()) {
                    boolean created = dbDir.mkdirs();
                    if (created) {
                        log.info("预创建数据库目录: {}", dbDir.getAbsolutePath());
                    } else {
                        log.warn("无法创建数据库目录: {}", dbDir.getAbsolutePath());
                    }
                }
            }

            // 创建存储桶目录
            if (properties.getBuckets() != null) {
                properties.getBuckets().forEach((bucketName, bucketConfig) -> {
                    if (bucketConfig.getStoragePath() != null) {
                        File storageDir = new File(bucketConfig.getStoragePath());
                        if (!storageDir.exists()) {
                            boolean created = storageDir.mkdirs();
                            if (created) {
                                log.info("预创建存储桶 {} 目录: {}", bucketName, storageDir.getAbsolutePath());
                            } else {
                                log.warn("无法创建存储桶 {} 目录: {}", bucketName, storageDir.getAbsolutePath());
                            }
                        }
                    }
                });
            }

            log.info("文件传输服务配置初始化完成 - 存储桶数量: {}, 用户数量: {}",
                    properties.getBuckets().size(), properties.getUsers().size());

        } catch (Exception e) {
            log.error("初始化配置失败", e);
            throw new RuntimeException("文件传输服务配置初始化失败", e);
        }
    }

    /**
     * 配置文件上传解析器
     */
    @Bean
    @ConditionalOnMissingBean(MultipartResolver.class)
    public MultipartResolver multipartResolver(FileTransferProperties properties) {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();

        // 使用默认存储桶配置
        FileTransferProperties.BucketConfig defaultConfig = properties.getDefaultConfig();

        // 设置最大上传文件大小
        resolver.setMaxUploadSize(defaultConfig.getMaxFileSize());

        // 设置最大内存大小（安全转换，确保不超过int最大值）
        long maxInMemorySize = defaultConfig.getMaxInMemorySize();
        if (maxInMemorySize > Integer.MAX_VALUE) {
            log.warn("maxInMemorySize值 {} 超过int最大值，将使用 {} ", maxInMemorySize, Integer.MAX_VALUE);
            resolver.setMaxInMemorySize(Integer.MAX_VALUE);
        } else {
            resolver.setMaxInMemorySize((int) maxInMemorySize);
        }

        // 设置默认编码
        resolver.setDefaultEncoding("UTF-8");

        // 延迟解析文件
        resolver.setResolveLazily(true);

        log.info("文件传输服务端SDK - 文件上传解析器配置完成，最大文件大小: {}MB, 最大内存: {}MB",
                defaultConfig.getMaxFileSize() / 1024 / 1024,
                defaultConfig.getMaxInMemorySize() / 1024 / 1024);

        return resolver;
    }
    
    /**
     * 初始化数据库
     */
    @Bean
    @ConditionalOnMissingBean(DatabaseInitializer.class)
    public DatabaseInitializer databaseInitializer() {
        return new DatabaseInitializer();
    }
} 