package com.sdesrd.filetransfer.server.config;

import java.time.LocalDateTime;

import javax.annotation.PostConstruct;

import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

/**
 * MyBatis类型处理器配置
 * 注册自定义的TypeHandler以处理SQLite时间戳格式
 */
@Slf4j
@Configuration
public class MyBatisTypeHandlerConfig {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @PostConstruct
    public void registerTypeHandlers() {
        try {
            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
            
            // 注册SQLite专用的LocalDateTime处理器
            registry.register(LocalDateTime.class, SqliteLocalDateTimeTypeHandler.class);
            
            log.info("成功注册SQLite LocalDateTime TypeHandler");
            
        } catch (Exception e) {
            log.error("注册TypeHandler失败", e);
        }
    }
} 