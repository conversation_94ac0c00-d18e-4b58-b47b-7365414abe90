package com.sdesrd.filetransfer.server.config;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库初始化器
 * 自动创建SQLite数据库和表结构
 */
@Slf4j
@Component
public class DatabaseInitializer implements ApplicationRunner {
    
    @Autowired
    private DataSource dataSource;
    
    private FileTransferProperties properties;
    
    /**
     * 无参构造函数（用于Spring Bean创建）
     */
    public DatabaseInitializer() {
        // Spring会通过setter或字段注入设置properties
    }
    
    /**
     * 有参构造函数（用于手动创建实例）
     */
    public DatabaseInitializer(FileTransferProperties properties) {
        this.properties = properties;
        ensureDatabaseDirectoryExists();
    }
    
    /**
     * 设置配置属性（Spring会调用此方法）
     */
    @Autowired
    public void setProperties(FileTransferProperties properties) {
        this.properties = properties;
        ensureDatabaseDirectoryExists();
    }
    
    /**
     * 确保数据库目录存在
     */
    private void ensureDatabaseDirectoryExists() {
        try {
            if (properties == null || properties.getDatabasePath() == null) {
                log.warn("数据库路径配置为空，使用默认路径");
                return;
            }

            String dbPath = properties.getDatabasePath();

            // 检查是否是内存数据库
            if (":memory:".equals(dbPath) || dbPath.contains(":memory:")) {
                log.info("使用内存数据库，跳过目录创建");
                return;
            }

            File dbFile = new File(dbPath);
            File dbDir = dbFile.getParentFile();

            if (dbDir != null && !dbDir.exists()) {
                boolean created = dbDir.mkdirs();
                if (created) {
                    log.info("创建数据库目录: {}", dbDir.getAbsolutePath());
                } else {
                    log.warn("无法创建数据库目录: {}", dbDir.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            log.error("创建数据库目录失败", e);
            // 不抛出异常，让数据源自己尝试处理
        }
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        initializeDatabase();
    }
    
    /**
     * 初始化数据库
     */
    private void initializeDatabase() {
        try {
            // 再次确保数据库目录存在（双重保险）
            ensureDatabaseDirectoryExists();
            
            // 创建数据库表（使用 IF NOT EXISTS，所以重复创建不会有问题）
            try (Connection connection = dataSource.getConnection()) {
                createTables(connection);
                log.info("数据库表创建完成");
            }
            
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }
    
    // 移除了 tableExists 方法，因为我们使用 CREATE TABLE IF NOT EXISTS
    
    /**
     * 创建数据库表
     */
    private void createTables(Connection connection) throws Exception {
        String createSql = getSqlScript();
        
        try (Statement statement = connection.createStatement()) {
            log.info("开始执行数据库初始化");
            
            // 预处理SQL：移除注释并重新格式化
            String processedSql = preprocessSql(createSql);
            log.info("预处理后的SQL长度: {}", processedSql.length());
            
            // 智能分割SQL语句并执行
            List<String> statements = splitSqlStatements(processedSql);
            log.info("分割后得到 {} 个SQL语句", statements.size());

            for (int i = 0; i < statements.size(); i++) {
                String sql = statements.get(i).trim();
                if (!sql.isEmpty()) {
                    String logSql = sql.length() > 100 ? sql.substring(0, 100) + "..." : sql;
                    log.info("执行第{}个SQL: {}", i + 1, logSql);
                    try {
                        statement.execute(sql);
                        log.info("第{}个SQL执行成功", i + 1);
                    } catch (Exception e) {
                        log.error("第{}个SQL执行失败: {}", i + 1, e.getMessage());
                        log.error("完整SQL语句: [{}]", sql);
                        log.error("SQL语句长度: {}", sql.length());
                        log.error("SQL语句字符: {}", sql.chars().mapToObj(c -> String.format("%c(%d)", (char)c, c)).limit(50).toArray());
                        throw e;
                    }
                }
            }
            
            log.info("数据库表创建完成");
        }
    }
    
    /**
     * 预处理SQL：移除注释和多余空白
     * 修复了多行注释和行内注释的处理逻辑
     */
    private String preprocessSql(String sql) {
        StringBuilder result = new StringBuilder();
        String[] lines = sql.split("\n");
        boolean inMultiLineComment = false;

        for (String line : lines) {
            String originalLine = line.trim();

            // 跳过空行
            if (originalLine.isEmpty()) {
                continue;
            }

            // 处理多行注释的开始和结束
            if (inMultiLineComment) {
                if (originalLine.contains("*/")) {
                    inMultiLineComment = false;
                    // 处理注释结束后的内容
                    int endIndex = originalLine.indexOf("*/");
                    if (endIndex + 2 < originalLine.length()) {
                        String remaining = originalLine.substring(endIndex + 2).trim();
                        if (!remaining.isEmpty() && !remaining.startsWith("--")) {
                            result.append(remaining).append(" ");
                        }
                    }
                }
                continue;
            }

            // 检查多行注释的开始
            if (originalLine.contains("/*")) {
                inMultiLineComment = true;
                // 处理注释开始前的内容
                int startIndex = originalLine.indexOf("/*");
                if (startIndex > 0) {
                    String beforeComment = originalLine.substring(0, startIndex).trim();
                    if (!beforeComment.isEmpty()) {
                        result.append(beforeComment).append(" ");
                    }
                }
                // 检查是否在同一行结束
                if (originalLine.contains("*/")) {
                    inMultiLineComment = false;
                    int endIndex = originalLine.indexOf("*/");
                    if (endIndex + 2 < originalLine.length()) {
                        String remaining = originalLine.substring(endIndex + 2).trim();
                        if (!remaining.isEmpty() && !remaining.startsWith("--")) {
                            result.append(remaining).append(" ");
                        }
                    }
                }
                continue;
            }

            // 处理单行注释
            if (originalLine.startsWith("--")) {
                continue;
            }

            // 移除行内单行注释
            int commentIndex = originalLine.indexOf("--");
            if (commentIndex >= 0) {
                // 确保不是在字符串内的注释
                String beforeComment = originalLine.substring(0, commentIndex).trim();
                if (!beforeComment.isEmpty()) {
                    result.append(beforeComment).append(" ");
                }
            } else {
                result.append(originalLine).append(" ");
            }
        }

        return result.toString().trim();
    }

    /**
     * 智能分割SQL语句
     * 正确处理字符串内的分号，避免错误分割
     */
    private List<String> splitSqlStatements(String sql) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;

        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);

            // 处理单引号
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
                currentStatement.append(c);
                continue;
            }

            // 处理双引号
            if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
                currentStatement.append(c);
                continue;
            }

            // 如果在字符串内，直接添加字符
            if (inSingleQuote || inDoubleQuote) {
                currentStatement.append(c);
                continue;
            }

            // 处理分号分割
            if (c == ';') {
                String statement = currentStatement.toString().trim();
                if (!statement.isEmpty()) {
                    statements.add(statement);
                }
                currentStatement = new StringBuilder();
            } else {
                currentStatement.append(c);
            }
        }

        // 添加最后一个语句（如果有的话）
        String lastStatement = currentStatement.toString().trim();
        if (!lastStatement.isEmpty()) {
            statements.add(lastStatement);
        }

        return statements;
    }
    
    /**
     * 获取SQL脚本内容
     */
    private String getSqlScript() throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource("sql/init-sqlite.sql");
            try (InputStream is = resource.getInputStream()) {
                byte[] bytes = StreamUtils.copyToByteArray(is);
                return new String(bytes, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            // 如果找不到SQL文件，使用内置的SQL
            return getDefaultSqlScript();
        }
    }
    
    /**
     * 获取默认的SQL脚本
     */
    private String getDefaultSqlScript() {
        return "-- 文件传输系统数据库初始化脚本（默认版本）\n" +
                "-- 版本: 5.1.0 (重新添加MD5字段用于传输完整性校验)\n" +
                "\n" +
                "-- 系统版本表\n" +
                "CREATE TABLE IF NOT EXISTS system_version (\n" +
                "    id INTEGER PRIMARY KEY,\n" +
                "    version TEXT NOT NULL,\n" +
                "    update_time TIMESTAMP NOT NULL\n" +
                ");\n" +
                "\n" +
                "-- 插入或更新版本信息\n" +
                "INSERT OR REPLACE INTO system_version (id, version, update_time) \n" +
                "VALUES (1, '5.1.0', strftime('%Y-%m-%d %H:%M:%f', 'now'));\n" +
                "\n" +
                "-- 对象存储映射表（简化设计，删除MD5字段）\n" +
                "CREATE TABLE IF NOT EXISTS object_storage (\n" +
                "    id TEXT PRIMARY KEY,\n" +
                "    bucket TEXT NOT NULL,\n" +
                "    object_key TEXT NOT NULL,\n" +
                "    file_id TEXT NOT NULL,\n" +
                "    file_name TEXT NOT NULL,\n" +
                "    file_size INTEGER NOT NULL,\n" +
                "    content_type TEXT,\n" +
                "    user_name TEXT NOT NULL,\n" +
                "    create_time TIMESTAMP NOT NULL,\n" +
                "    update_time TIMESTAMP NOT NULL\n" +
                ");\n" +
                "\n" +
                "-- 创建索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_object_bucket_key ON object_storage(bucket, object_key);\n" +
                "CREATE INDEX IF NOT EXISTS idx_object_user ON object_storage(user_name);\n" +
                "CREATE INDEX IF NOT EXISTS idx_object_file_id ON object_storage(file_id);\n" +
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_bucket_key ON object_storage(bucket, object_key);\n" +
                "\n" +
                "-- 传输任务表（用于跟踪上传进度，包含MD5校验字段）\n" +
                "CREATE TABLE IF NOT EXISTS transfer_task (\n" +
                "    transfer_id TEXT PRIMARY KEY,\n" +
                "    bucket TEXT NOT NULL,\n" +
                "    object_key TEXT NOT NULL,\n" +
                "    file_name TEXT NOT NULL,\n" +
                "    file_size INTEGER NOT NULL,\n" +
                "    file_md5 TEXT,\n" +
                "    user_name TEXT NOT NULL,\n" +
                "    chunk_size INTEGER NOT NULL,\n" +
                "    total_chunks INTEGER NOT NULL,\n" +
                "    completed_chunks INTEGER DEFAULT 0,\n" +
                "    status INTEGER DEFAULT 0,\n" +
                "    create_time TIMESTAMP NOT NULL,\n" +
                "    update_time TIMESTAMP NOT NULL,\n" +
                "    complete_time TIMESTAMP\n" +
                ");\n" +
                "\n" +
                "-- 创建传输任务索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_transfer_user ON transfer_task(user_name);\n" +
                "CREATE INDEX IF NOT EXISTS idx_transfer_status ON transfer_task(status);\n" +
                "CREATE INDEX IF NOT EXISTS idx_transfer_bucket_key ON transfer_task(bucket, object_key);\n" +
                "\n" +
                "-- 分块记录表（删除MD5字段）\n" +
                "CREATE TABLE IF NOT EXISTS chunk_record (\n" +
                "    id TEXT PRIMARY KEY,\n" +
                "    transfer_id TEXT NOT NULL,\n" +
                "    chunk_index INTEGER NOT NULL,\n" +
                "    chunk_size INTEGER NOT NULL,\n" +
                "    chunk_path TEXT NOT NULL,\n" +
                "    status INTEGER DEFAULT 0,\n" +
                "    create_time TIMESTAMP NOT NULL,\n" +
                "    FOREIGN KEY (transfer_id) REFERENCES transfer_task(transfer_id)\n" +
                ");\n" +
                "\n" +
                "-- 创建分块索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_chunk_transfer ON chunk_record(transfer_id);\n" +
                "CREATE INDEX IF NOT EXISTS idx_chunk_status ON chunk_record(status);\n" +
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_transfer_chunk ON chunk_record(transfer_id, chunk_index);\n" +
                "\n" +
                "-- 下载任务表（用于支持断点续传的分片下载）\n" +
                "CREATE TABLE IF NOT EXISTS download_task (\n" +
                "    download_id TEXT PRIMARY KEY,\n" +
                "    bucket TEXT NOT NULL,\n" +
                "    object_key TEXT NOT NULL,\n" +
                "    file_name TEXT NOT NULL,\n" +
                "    file_size INTEGER NOT NULL,\n" +
                "    file_md5 TEXT,\n" +
                "    user_name TEXT NOT NULL,\n" +
                "    chunk_size INTEGER NOT NULL,\n" +
                "    total_chunks INTEGER NOT NULL,\n" +
                "    downloaded_chunks INTEGER DEFAULT 0,\n" +
                "    status INTEGER DEFAULT 0,\n" +
                "    client_ip TEXT,\n" +
                "    create_time TIMESTAMP NOT NULL,\n" +
                "    update_time TIMESTAMP NOT NULL,\n" +
                "    complete_time TIMESTAMP\n" +
                ");\n" +
                "\n" +
                "-- 创建下载任务索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_download_user ON download_task(user_name);\n" +
                "CREATE INDEX IF NOT EXISTS idx_download_status ON download_task(status);\n" +
                "CREATE INDEX IF NOT EXISTS idx_download_bucket_key ON download_task(bucket, object_key);\n" +
                "\n" +
                "-- 下载分块记录表（用于跟踪分片下载的每个分块状态）\n" +
                "CREATE TABLE IF NOT EXISTS download_chunk_record (\n" +
                "    id TEXT PRIMARY KEY,\n" +
                "    download_id TEXT NOT NULL,\n" +
                "    chunk_index INTEGER NOT NULL,\n" +
                "    chunk_size INTEGER NOT NULL,\n" +
                "    start_position INTEGER NOT NULL,\n" +
                "    end_position INTEGER NOT NULL,\n" +
                "    status INTEGER DEFAULT 0,\n" +
                "    retry_count INTEGER DEFAULT 0,\n" +
                "    max_retry_count INTEGER DEFAULT 3,\n" +
                "    error_message TEXT,\n" +
                "    create_time TIMESTAMP NOT NULL,\n" +
                "    update_time TIMESTAMP NOT NULL,\n" +
                "    download_time TIMESTAMP,\n" +
                "    FOREIGN KEY (download_id) REFERENCES download_task(download_id)\n" +
                ");\n" +
                "\n" +
                "-- 创建下载分块记录索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_download_chunk_task ON download_chunk_record(download_id);\n" +
                "CREATE INDEX IF NOT EXISTS idx_download_chunk_status ON download_chunk_record(status);\n" +
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_download_chunk ON download_chunk_record(download_id, chunk_index);";
    }
} 