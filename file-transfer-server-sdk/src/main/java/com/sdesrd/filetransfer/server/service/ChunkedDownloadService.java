package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.time.LocalDateTime;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.DownloadChunkRecord;
import com.sdesrd.filetransfer.server.entity.DownloadTask;
import com.sdesrd.filetransfer.server.entity.ObjectStorage;
import com.sdesrd.filetransfer.server.mapper.DownloadChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.DownloadTaskMapper;
import com.sdesrd.filetransfer.server.mapper.ObjectStorageMapper;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 分片下载服务
 * 提供支持断点续传的分片下载功能
 */
@Slf4j
@Service
public class ChunkedDownloadService {
    
    /** 默认分块大小：2MB */
    private static final int DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024;
    
    /** 最大重试次数 */
    private static final int DEFAULT_MAX_RETRY_COUNT = 3;
    
    @Autowired
    private DownloadTaskMapper downloadTaskMapper;
    
    @Autowired
    private DownloadChunkRecordMapper downloadChunkRecordMapper;
    
    @Autowired
    private ObjectStorageMapper objectStorageMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    /**
     * 初始化分片下载任务
     * 
     * @param bucket 存储桶名称
     * @param objectKey 对象key
     * @param username 用户名
     * @param clientIp 客户端IP
     * @param chunkSize 分块大小（可选，默认2MB）
     * @return 下载任务ID
     */
    @Transactional
    public String initChunkedDownload(String bucket, String objectKey, String username, String clientIp, Integer chunkSize) {
        // 验证权限
        if (!properties.isUserAllowedToBucket(username, bucket)) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + bucket);
        }
        
        // 查找对象
        ObjectStorage objectStorage = objectStorageMapper.findByBucketAndKey(bucket, objectKey);
        if (objectStorage == null) {
            throw new IllegalArgumentException("对象不存在: " + bucket + "/" + objectKey);
        }
        
        // 检查文件是否存在
        String physicalPath = getPhysicalFilePath(bucket, objectKey);
        File file = new File(physicalPath);
        if (!file.exists()) {
            throw new IllegalArgumentException("物理文件不存在: " + physicalPath);
        }
        
        // 使用默认分块大小或用户指定的分块大小
        int actualChunkSize = chunkSize != null ? chunkSize : DEFAULT_CHUNK_SIZE;
        long fileSize = file.length();
        int totalChunks = (int) Math.ceil((double) fileSize / actualChunkSize);
        
        // 创建下载任务
        DownloadTask downloadTask = new DownloadTask();
        downloadTask.setDownloadId(DownloadTask.generateDownloadId());
        downloadTask.setBucket(bucket);
        downloadTask.setObjectKey(objectKey);
        downloadTask.setFileName(objectStorage.getFileName());
        downloadTask.setFileSize(fileSize);
        downloadTask.setFileMd5(null); // 可以后续添加MD5校验
        downloadTask.setUserName(username);
        downloadTask.setChunkSize(actualChunkSize);
        downloadTask.setTotalChunks(totalChunks);
        downloadTask.setDownloadedChunks(0);
        downloadTask.setStatus(DownloadTask.Status.INITIALIZED);
        downloadTask.setClientIp(clientIp);
        downloadTask.setCreateTime(LocalDateTime.now());
        downloadTask.setUpdateTime(LocalDateTime.now());
        
        downloadTaskMapper.insert(downloadTask);
        
        // 创建分块记录
        createChunkRecords(downloadTask.getDownloadId(), fileSize, actualChunkSize, totalChunks);
        
        log.info("初始化分片下载任务成功 - downloadId: {}, bucket: {}, key: {}, fileSize: {}, chunks: {}", 
                downloadTask.getDownloadId(), bucket, objectKey, fileSize, totalChunks);
        
        return downloadTask.getDownloadId();
    }
    
    /**
     * 下载指定分块
     * 
     * @param downloadId 下载任务ID
     * @param chunkIndex 分块索引
     * @param username 用户名
     * @param response HTTP响应
     */
    public void downloadChunk(String downloadId, Integer chunkIndex, String username, HttpServletResponse response) {
        // 验证下载任务
        DownloadTask downloadTask = downloadTaskMapper.selectById(downloadId);
        if (downloadTask == null || !downloadTask.getUserName().equals(username)) {
            throw new IllegalArgumentException("下载任务不存在或无权限");
        }
        
        // 查找分块记录
        DownloadChunkRecord chunkRecord = downloadChunkRecordMapper.findByDownloadIdAndIndex(downloadId, chunkIndex);
        if (chunkRecord == null) {
            throw new IllegalArgumentException("分块不存在: " + chunkIndex);
        }
        
        try {
            // 更新分块状态为下载中
            downloadChunkRecordMapper.updateChunkStatus(downloadId, chunkIndex, DownloadChunkRecord.Status.DOWNLOADING);
            
            // 获取配置
            FileTransferProperties.EffectiveConfig config = properties.getEffectiveConfig(username, downloadTask.getBucket());
            
            // 执行分块下载
            performChunkDownload(downloadTask, chunkRecord, config, response);
            
            // 更新分块状态为已下载
            downloadChunkRecordMapper.updateChunkStatusWithDownloadTime(downloadId, chunkIndex, DownloadChunkRecord.Status.DOWNLOADED);
            
            // 更新下载任务进度
            updateDownloadProgress(downloadId);
            
        } catch (Exception e) {
            log.error("下载分块失败 - downloadId: {}, chunkIndex: {}", downloadId, chunkIndex, e);
            
            // 更新重试信息
            chunkRecord.incrementRetryCount();
            int newStatus = chunkRecord.canRetry() ? DownloadChunkRecord.Status.PENDING : DownloadChunkRecord.Status.FAILED;
            downloadChunkRecordMapper.updateChunkRetryInfo(downloadId, chunkIndex, chunkRecord.getRetryCount(), e.getMessage());
            downloadChunkRecordMapper.updateChunkStatus(downloadId, chunkIndex, newStatus);
            
            throw new RuntimeException("下载分块失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取下载任务状态
     * 
     * @param downloadId 下载任务ID
     * @param username 用户名
     * @return 下载任务
     */
    public DownloadTask getDownloadStatus(String downloadId, String username) {
        DownloadTask downloadTask = downloadTaskMapper.selectById(downloadId);
        if (downloadTask == null || !downloadTask.getUserName().equals(username)) {
            throw new IllegalArgumentException("下载任务不存在或无权限");
        }
        return downloadTask;
    }
    
    /**
     * 获取下载任务的分块列表
     * 
     * @param downloadId 下载任务ID
     * @param username 用户名
     * @return 分块记录列表
     */
    public List<DownloadChunkRecord> getDownloadChunks(String downloadId, String username) {
        DownloadTask downloadTask = downloadTaskMapper.selectById(downloadId);
        if (downloadTask == null || !downloadTask.getUserName().equals(username)) {
            throw new IllegalArgumentException("下载任务不存在或无权限");
        }
        return downloadChunkRecordMapper.findByDownloadId(downloadId);
    }
    
    /**
     * 暂停下载任务
     * 
     * @param downloadId 下载任务ID
     * @param username 用户名
     */
    public void pauseDownload(String downloadId, String username) {
        int updated = downloadTaskMapper.pauseDownload(downloadId, username);
        if (updated == 0) {
            throw new IllegalArgumentException("无法暂停下载任务，任务不存在或状态不正确");
        }
        log.info("暂停下载任务 - downloadId: {}, user: {}", downloadId, username);
    }
    
    /**
     * 恢复下载任务
     * 
     * @param downloadId 下载任务ID
     * @param username 用户名
     */
    public void resumeDownload(String downloadId, String username) {
        int updated = downloadTaskMapper.resumeDownload(downloadId, username);
        if (updated == 0) {
            throw new IllegalArgumentException("无法恢复下载任务，任务不存在或状态不正确");
        }
        log.info("恢复下载任务 - downloadId: {}, user: {}", downloadId, username);
    }
    
    /**
     * 取消下载任务
     * 
     * @param downloadId 下载任务ID
     * @param username 用户名
     */
    public void cancelDownload(String downloadId, String username) {
        int updated = downloadTaskMapper.cancelDownload(downloadId, username);
        if (updated == 0) {
            throw new IllegalArgumentException("无法取消下载任务，任务不存在或状态不正确");
        }
        log.info("取消下载任务 - downloadId: {}, user: {}", downloadId, username);
    }
    
    // === 私有辅助方法 ===
    
    /**
     * 创建分块记录
     */
    private void createChunkRecords(String downloadId, long fileSize, int chunkSize, int totalChunks) {
        for (int i = 0; i < totalChunks; i++) {
            long startPosition = (long) i * chunkSize;
            long endPosition = Math.min(startPosition + chunkSize - 1, fileSize - 1);
            long actualChunkSize = endPosition - startPosition + 1;
            
            DownloadChunkRecord chunkRecord = new DownloadChunkRecord();
            chunkRecord.setId(DownloadChunkRecord.generateId());
            chunkRecord.setDownloadId(downloadId);
            chunkRecord.setChunkIndex(i);
            chunkRecord.setChunkSize(actualChunkSize);
            chunkRecord.setStartPosition(startPosition);
            chunkRecord.setEndPosition(endPosition);
            chunkRecord.setStatus(DownloadChunkRecord.Status.PENDING);
            chunkRecord.setRetryCount(0);
            chunkRecord.setMaxRetryCount(DEFAULT_MAX_RETRY_COUNT);
            chunkRecord.setCreateTime(LocalDateTime.now());
            chunkRecord.setUpdateTime(LocalDateTime.now());
            
            downloadChunkRecordMapper.insert(chunkRecord);
        }
    }
    
    /**
     * 执行分块下载
     */
    private void performChunkDownload(DownloadTask downloadTask, DownloadChunkRecord chunkRecord, 
                                    FileTransferProperties.EffectiveConfig config, HttpServletResponse response) throws IOException {
        String physicalPath = getPhysicalFilePath(downloadTask.getBucket(), downloadTask.getObjectKey());
        File file = new File(physicalPath);
        
        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + downloadTask.getFileName() + ".chunk" + chunkRecord.getChunkIndex() + "\"");
        response.setContentLengthLong(chunkRecord.getActualChunkSize());
        
        // 添加分块信息到响应头
        response.setHeader("X-Chunk-Index", String.valueOf(chunkRecord.getChunkIndex()));
        response.setHeader("X-Chunk-Size", String.valueOf(chunkRecord.getActualChunkSize()));
        response.setHeader("X-Start-Position", String.valueOf(chunkRecord.getStartPosition()));
        response.setHeader("X-End-Position", String.valueOf(chunkRecord.getEndPosition()));
        
        // 使用RandomAccessFile进行分块读取
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(chunkRecord.getStartPosition());
            
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            long remaining = chunkRecord.getActualChunkSize();
            
            while (remaining > 0) {
                int toRead = (int) Math.min(buffer.length, remaining);
                int bytesRead = raf.read(buffer, 0, toRead);
                
                if (bytesRead == -1) {
                    break;
                }
                
                // 应用限速
                if (config.isRateLimitEnabled()) {
                    RateLimitUtils.applyRateLimit(downloadTask.getUserName() + "_download", config.getDownloadRateLimit(), bytesRead);
                }
                
                response.getOutputStream().write(buffer, 0, bytesRead);
                remaining -= bytesRead;
            }
            
            response.getOutputStream().flush();
        }
    }
    
    /**
     * 更新下载进度
     */
    private void updateDownloadProgress(String downloadId) {
        int downloadedChunks = downloadChunkRecordMapper.countDownloadedChunks(downloadId);
        DownloadTask downloadTask = downloadTaskMapper.selectById(downloadId);
        
        if (downloadTask != null) {
            int newStatus = downloadedChunks >= downloadTask.getTotalChunks() ? 
                           DownloadTask.Status.COMPLETED : DownloadTask.Status.DOWNLOADING;
            
            downloadTaskMapper.updateProgress(downloadId, downloadedChunks, newStatus);
            
            if (newStatus == DownloadTask.Status.COMPLETED) {
                downloadTaskMapper.completeDownload(downloadId, DownloadTask.Status.COMPLETED);
                log.info("下载任务完成 - downloadId: {}", downloadId);
            }
        }
    }
    
    /**
     * 获取物理文件路径
     */
    private String getPhysicalFilePath(String bucket, String key) {
        FileTransferProperties.BucketConfig bucketConfig = properties.getBucketConfig(bucket);
        if (bucketConfig == null) {
            throw new IllegalArgumentException("存储桶配置不存在: " + bucket);
        }
        return bucketConfig.getStoragePath() + "/" + key;
    }
}
