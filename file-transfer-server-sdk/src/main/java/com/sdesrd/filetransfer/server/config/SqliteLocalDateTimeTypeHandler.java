package com.sdesrd.filetransfer.server.config;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import lombok.extern.slf4j.Slf4j;

/**
 * SQLite专用的LocalDateTime类型处理器
 * 处理SQLite TIMESTAMP字段的ISO 8601格式转换
 */
@Slf4j
@MappedTypes(LocalDateTime.class)
@MappedJdbcTypes(JdbcType.TIMESTAMP)
public class SqliteLocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {
    
    // SQLite中常见的时间格式
    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
    private static final DateTimeFormatter ISO_FORMATTER_NO_MILLIS = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    private static final DateTimeFormatter SQL_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter SQL_FORMATTER_NO_MILLIS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        // 设置参数时使用Timestamp，让JDBC驱动自动处理格式
        ps.setTimestamp(i, Timestamp.valueOf(parameter));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseDateTime(rs.getString(columnName));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseDateTime(rs.getString(columnIndex));
    }

    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseDateTime(cs.getString(columnIndex));
    }
    
    /**
     * 解析日期时间字符串，支持多种格式
     */
    private LocalDateTime parseDateTime(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return null;
        }
        
        String trimmed = dateTimeString.trim();
        
        try {
            // 处理ISO 8601格式的高精度时间戳
            if (trimmed.contains("T")) {
                // SQLite可能产生纳秒精度的时间戳，我们需要截断到毫秒精度
                String normalized = normalizeTimestamp(trimmed);
                try {
                    return LocalDateTime.parse(normalized, ISO_FORMATTER);
                } catch (DateTimeParseException e) {
                    // 尝试无毫秒的ISO格式
                    return LocalDateTime.parse(normalized.substring(0, 19), ISO_FORMATTER_NO_MILLIS);
                }
            }
            
            // 尝试解析SQL标准格式 (2025-06-19 02:43:07.141)
            try {
                return LocalDateTime.parse(trimmed, SQL_FORMATTER);
            } catch (DateTimeParseException e) {
                // 尝试无毫秒的SQL格式
                return LocalDateTime.parse(trimmed, SQL_FORMATTER_NO_MILLIS);
            }
            
        } catch (DateTimeParseException e) {
            log.warn("无法解析日期时间字符串: {}, 使用当前时间替代", trimmed, e);
            return LocalDateTime.now();
        }
    }
    
    /**
     * 规范化时间戳格式，将纳秒精度截断为毫秒精度
     */
    private String normalizeTimestamp(String timestamp) {
        // 查找小数点位置
        int dotIndex = timestamp.indexOf('.');
        if (dotIndex == -1) {
            return timestamp; // 没有小数部分
        }
        
        // 查找小数部分的结束位置（可能有时区信息）
        int endIndex = timestamp.length();
        for (int i = dotIndex + 1; i < timestamp.length(); i++) {
            char c = timestamp.charAt(i);
            if (!Character.isDigit(c)) {
                endIndex = i;
                break;
            }
        }
        
        String fractional = timestamp.substring(dotIndex + 1, endIndex);
        String suffix = timestamp.substring(endIndex);
        
        // 截断或补齐到3位毫秒
        if (fractional.length() > 3) {
            fractional = fractional.substring(0, 3);
        } else if (fractional.length() < 3) {
            fractional = String.format("%-3s", fractional).replace(' ', '0');
        }
        
        return timestamp.substring(0, dotIndex) + "." + fractional + suffix;
    }
} 