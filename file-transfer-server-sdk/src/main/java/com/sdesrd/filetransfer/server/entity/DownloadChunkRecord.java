package com.sdesrd.filetransfer.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 下载分块记录实体
 * 用于跟踪分片下载的每个分块状态
 */
@Data
@TableName("download_chunk_record")
public class DownloadChunkRecord {
    
    /**
     * 记录ID
     */
    @TableId
    private String id;
    
    /**
     * 下载任务ID
     */
    private String downloadId;
    
    /**
     * 分块索引（从0开始）
     */
    private Integer chunkIndex;
    
    /**
     * 分块大小
     */
    private Long chunkSize;
    
    /**
     * 分块在文件中的起始位置
     */
    private Long startPosition;
    
    /**
     * 分块在文件中的结束位置
     */
    private Long endPosition;
    
    /**
     * 分块状态：0-待下载，1-下载中，2-已下载，3-下载失败
     */
    private Integer status;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;
    
    /**
     * 错误信息（如果下载失败）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 下载完成时间
     */
    private LocalDateTime downloadTime;
    
    /**
     * 分块状态常量
     */
    public static class Status {
        /** 待下载状态 */
        public static final int PENDING = 0;
        /** 下载中状态 */
        public static final int DOWNLOADING = 1;
        /** 已下载状态 */
        public static final int DOWNLOADED = 2;
        /** 下载失败状态 */
        public static final int FAILED = 3;
    }
    
    /**
     * 生成分块记录ID
     * 
     * @return 唯一的分块记录ID
     */
    public static String generateId() {
        return "dc_" + java.util.UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 检查是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        if (maxRetryCount == null || retryCount == null) {
            return true;
        }
        return retryCount < maxRetryCount;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 1;
        } else {
            retryCount++;
        }
    }
    
    /**
     * 检查分块是否已下载
     * 
     * @return 是否已下载
     */
    public boolean isDownloaded() {
        return Status.DOWNLOADED == status;
    }
    
    /**
     * 检查分块是否下载失败
     * 
     * @return 是否下载失败
     */
    public boolean isFailed() {
        return Status.FAILED == status;
    }
    
    /**
     * 检查分块是否正在下载
     * 
     * @return 是否正在下载
     */
    public boolean isDownloading() {
        return Status.DOWNLOADING == status;
    }
    
    /**
     * 计算分块大小（实际大小）
     * 
     * @return 分块大小
     */
    public long getActualChunkSize() {
        if (startPosition == null || endPosition == null) {
            return chunkSize != null ? chunkSize : 0L;
        }
        return endPosition - startPosition + 1;
    }
}
