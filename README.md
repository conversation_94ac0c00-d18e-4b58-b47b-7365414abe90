# 文件传输SDK (S3风格版本)

一个基于Java的轻量级文件传输解决方案，提供类似AWS S3的对象存储API，支持存储桶管理、用户权限控制、断点续传和限速传输。

## 核心特性

- ✅ **S3风格API**: 类似AWS S3的REST接口，使用bucket+key模式
- ✅ **存储桶管理**: 在配置文件中定义存储桶，灵活的存储策略
- ✅ **用户权限控制**: 基于HMAC-SHA256认证，细粒度的存储桶访问权限
- ✅ **断点续传**: 支持文件上传下载中断后继续传输
- ✅ **分块传输**: 大文件自动分块，提高传输可靠性
- ✅ **分片下载**: 支持断点续传的分片下载，可暂停/恢复/取消
- ✅ **智能重传**: 可配置的重传策略，支持指数退避和抖动算法
- ✅ **并发传输**: 支持并发分片上传和下载，提高传输效率
- ✅ **角色限速**: 基于用户角色的精细化速度控制
- ✅ **秒传功能**: 基于MD5去重，避免重复上传
- ✅ **差异化限速**: 不同用户和存储桶的传输速度控制
- ✅ **文件去重**: 相同内容的文件共享物理存储，节省空间
- ✅ **轻量化设计**: 简洁的数据库设计，SQLite嵌入式数据库

## 项目结构

```
file-transfer-sdk/
├── file-transfer-server-sdk/         # 服务端SDK
├── file-transfer-client-sdk/         # 客户端SDK  
├── file-transfer-demo/               # 演示项目
├── file-transfer-server-standalone/  # 独立服务端应用
└── example-auth-config.yml          # 配置示例
```

## 快速开始

### 1. 编译项目

```bash
mvn clean install
```

### 2. 配置服务端

参考 [example-auth-config.yml](example-auth-config.yml) 配置存储桶和用户：

```yaml
file:
  transfer:
    server:
      # 存储桶配置
      buckets:
        default:
          name: "default"
          storage-path: "./data/buckets/default"
          upload-rate-limit: 10485760   # 10MB/s
          max-file-size: 104857600      # 100MB
          
        documents:
          name: "documents"  
          storage-path: "./data/buckets/documents"
          upload-rate-limit: 5242880    # 5MB/s
          max-file-size: 52428800       # 50MB
      
      # 用户配置
      users:
        user1:
          secret-key: "user1-secret-2024"
          allowed-buckets: ["default"]
          
        vip:
          secret-key: "vip-secret-2024"
          allowed-buckets: ["default", "documents"]
```

### 3. 运行演示项目

```bash
cd file-transfer-demo
mvn spring-boot:run
```

## S3风格API

### 认证

每个请求需要在HTTP头中提供认证信息：

```
X-File-Transfer-User: user1
X-File-Transfer-Auth: <HMAC-SHA256签名>
```

### 主要接口

| 方法 | 路径 | 功能 |
|------|------|------|
| `POST` | `/filetransfer/api/s3/upload/init` | 初始化文件上传 |
| `POST` | `/filetransfer/api/s3/upload/chunk` | 上传分块 |
| `POST` | `/filetransfer/api/s3/upload/complete` | 完成上传 |
| `GET` | `/filetransfer/api/s3/{bucket}/{key}` | 下载文件 |
| `HEAD` | `/filetransfer/api/s3/{bucket}/{key}` | 获取文件元数据 |
| `GET` | `/filetransfer/api/s3/` | 列出对象 |
| `DELETE` | `/filetransfer/api/s3/{bucket}/{key}` | 删除文件 |

### 使用示例

#### 上传文件

```bash
# 1. 初始化上传
curl -X POST "http://localhost:49011/filetransfer/api/s3/upload/init" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "bucket": "default",
    "key": "documents/report.pdf",
    "fileName": "report.pdf",
    "fileSize": 1048576,
    "contentMd5": "abc123..."
  }'

# 2. 上传分块
curl -X POST "http://localhost:49011/filetransfer/api/s3/upload/chunk" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>" \
  -F "transferId=<transfer-id>" \
  -F "chunkIndex=0" \
  -F "chunkMd5=<chunk-md5>" \
  -F "chunk=@chunk_0.bin"

# 3. 完成上传  
curl -X POST "http://localhost:49011/filetransfer/api/s3/upload/complete?transferId=<id>&bucket=default&key=documents/report.pdf" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>"
```

#### 下载文件

```bash
curl -X GET "http://localhost:49011/filetransfer/api/s3/default/documents/report.pdf" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>" \
  -o "local-report.pdf"
```

#### 列出文件

```bash
# 列出存储桶中的所有文件
curl -X GET "http://localhost:49011/filetransfer/api/s3/?bucket=default" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>"

# 列出指定前缀的文件（目录风格）
curl -X GET "http://localhost:49011/filetransfer/api/s3/?bucket=default&prefix=documents/&delimiter=/" \
  -H "X-File-Transfer-User: user1" \
  -H "X-File-Transfer-Auth: <auth-token>"
```

## 客户端SDK

### 添加依赖

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-client-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 使用示例

```java
// 创建客户端配置
ClientConfig config = new ClientConfig();
config.getAuth().setServerAddr("localhost");
config.getAuth().setServerPort(49011);
config.getAuth().setUser("user1");
config.getAuth().setSecretKey("user1-secret-2024");

// 创建S3风格客户端
S3FileTransferClient client = new S3FileTransferClient(config);

// 上传文件
S3PutObjectRequest request = new S3PutObjectRequest();
request.setBucket("default");
request.setKey("documents/report.pdf");
request.setFileName("report.pdf");
request.setFileSize(1048576L);

S3PutObjectResult result = client.putObject(request, "/local/path/report.pdf", listener);

// 下载文件
S3GetObjectResult downloadResult = client.getObject("default", "documents/report.pdf", "/local/download/report.pdf", listener);

// 列出文件
S3ListObjectsResult listResult = client.listObjects("default", "documents/", "/", 100);
```

## 存储桶概念

### 设计理念

- **存储桶配置化**: 在配置文件中定义存储桶，每个存储桶有独立的存储路径和传输策略
- **用户权限控制**: 通过`allowed-buckets`配置用户可访问的存储桶
- **配置继承**: 用户级配置覆盖存储桶默认配置
- **物理文件共享**: 相同MD5的文件只存储一份，不同存储桶可以引用同一个物理文件

### 存储结构

```
物理存储路径: ${bucket.storage-path}/{key}
逻辑映射: bucket + key -> 物理文件

示例:
- 存储桶: documents
- Key: reports/2024/annual.pdf
- 物理路径: ./data/buckets/documents/reports/2024/annual.pdf
```

**注意**: 新的存储结构直接使用key作为相对路径，简化了文件组织方式，提高了可读性和可维护性。

## 数据库设计

简化的数据库设计，包含3个核心表：

1. **object_storage**: 对象存储映射表，记录bucket+key到物理文件的映射
2. **transfer_task**: 传输任务表，跟踪上传进度
3. **chunk_record**: 分块记录表，管理文件分块

## 技术栈

- **Java**: 8+
- **Spring Boot**: 2.6.6
- **MyBatis Plus**: 3.5.6
- **SQLite**: 嵌入式数据库
- **OkHttp**: HTTP客户端

## 部署指南

### 独立部署

```bash
cd file-transfer-server-standalone
mvn clean package
java -jar target/file-transfer-server-standalone-1.0.0.jar
```

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY target/file-transfer-server-standalone-1.0.0.jar app.jar
EXPOSE 49011
VOLUME ["/data"]
CMD ["java", "-jar", "app.jar"]
```

## 配置参数

### 存储桶配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `name` | 存储桶名称 | - |
| `storage-path` | 文件存储路径 | - |
| `upload-rate-limit` | 上传限速(字节/秒) | 10MB/s |
| `download-rate-limit` | 下载限速(字节/秒) | 10MB/s |
| `max-file-size` | 最大文件大小 | 100MB |
| `fast-upload-enabled` | 是否启用秒传 | true |

### 用户配置

| 参数 | 说明 | 是否必填 |
|------|------|--------|
| `secret-key` | 用户密钥 | ✅ 必填 |
| `allowed-buckets` | 可访问的存储桶列表 | ✅ 必填 |
| `upload-rate-limit` | 用户级上传限速 | 可选 |
| `download-rate-limit` | 用户级下载限速 | 可选 |
| `max-file-size` | 用户级最大文件大小 | 可选 |

## 注意事项

1. **权限控制**: 用户只能访问配置中允许的存储桶
2. **文件去重**: 相同MD5的文件会共享物理存储
3. **配置优先级**: 用户配置 > 存储桶配置
4. **路径安全**: 系统会自动验证路径安全性
5. **认证令牌**: 5分钟有效期，过期需重新生成

## 最佳实践

### 存储桶规划

```yaml
buckets:
  # 临时文件存储桶 - 短期存储
  temp:
    storage-path: "./data/temp"
    max-file-size: 10485760      # 10MB
    
  # 文档存储桶 - 长期存储
  documents:
    storage-path: "./data/documents"
    max-file-size: 104857600     # 100MB
    
  # 媒体存储桶 - 大文件存储
  media:
    storage-path: "./data/media"
    max-file-size: 1073741824    # 1GB
```

### 用户权限设计

```yaml
users:
  # 临时用户 - 只能上传小文件
  guest:
    allowed-buckets: ["temp"]
    max-file-size: 1048576       # 1MB
    
  # 普通用户 - 文档和临时文件
  user:
    allowed-buckets: ["temp", "documents"]
    
  # 高级用户 - 所有存储桶
  premium:
    allowed-buckets: ["temp", "documents", "media"]
```

## 相关文档

- [自动化构建指南](BUILD_AND_TEST_GUIDE.md)
- [集成指南](integration-guide.md)
- [配置示例](example-auth-config.yml)
