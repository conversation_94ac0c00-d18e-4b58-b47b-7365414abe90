package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 分片下载请求
 */
@Data
public class ChunkedDownloadRequest {
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 对象key
     */
    private String key;
    
    /**
     * 本地保存路径
     */
    private String localPath;
    
    /**
     * 分块大小（可选，默认使用服务端配置）
     */
    private Integer chunkSize;
    
    /**
     * 是否覆盖已存在的本地文件
     */
    private boolean overwrite = false;
    
    /**
     * 是否启用断点续传
     */
    private boolean resumeEnabled = true;
    
    /**
     * 最大并发下载分块数
     */
    private int maxConcurrentChunks = 3;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (bucket == null || bucket.trim().isEmpty()) {
            throw new IllegalArgumentException("存储桶名称不能为空");
        }
        
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("对象key不能为空");
        }
        
        if (localPath == null || localPath.trim().isEmpty()) {
            throw new IllegalArgumentException("本地保存路径不能为空");
        }
        
        if (chunkSize != null && chunkSize <= 0) {
            throw new IllegalArgumentException("分块大小必须大于0");
        }
        
        if (maxConcurrentChunks <= 0) {
            throw new IllegalArgumentException("最大并发分块数必须大于0");
        }
    }
}
