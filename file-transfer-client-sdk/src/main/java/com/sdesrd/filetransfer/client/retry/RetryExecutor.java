package com.sdesrd.filetransfer.client.retry;

import java.util.concurrent.Callable;
import java.util.function.Consumer;

import lombok.extern.slf4j.Slf4j;

/**
 * 重传执行器
 * 负责执行重传逻辑，支持各种重传策略
 */
@Slf4j
public class RetryExecutor {
    
    private final RetryPolicy retryPolicy;
    
    /**
     * 构造函数
     * 
     * @param retryPolicy 重传策略
     */
    public RetryExecutor(RetryPolicy retryPolicy) {
        this.retryPolicy = retryPolicy;
        this.retryPolicy.validate(); // 验证策略配置
    }
    
    /**
     * 执行带重传的操作（无返回值）
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @throws Exception 如果所有重试都失败
     */
    public void execute(RunnableWithException operation, String operationName) throws Exception {
        execute(() -> {
            operation.run();
            return null;
        }, operationName);
    }
    
    /**
     * 执行带重传的操作（有返回值）
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public <T> T execute(Callable<T> operation, String operationName) throws Exception {
        return execute(operation, operationName, null);
    }
    
    /**
     * 执行带重传的操作（有返回值和重试回调）
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param retryCallback 重试回调函数（可选）
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public <T> T execute(Callable<T> operation, String operationName, Consumer<RetryContext> retryCallback) throws Exception {
        Exception lastException = null;
        int retryCount = 0;
        
        while (true) {
            try {
                // 执行操作
                T result = operation.call();
                
                // 如果是重试成功，记录日志
                if (retryCount > 0) {
                    log.info("操作重试成功 - 操作: {}, 重试次数: {}", operationName, retryCount);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                // 检查是否可重试
                if (!retryPolicy.isRetryableException(e)) {
                    log.warn("操作失败，异常不可重试 - 操作: {}, 异常: {}", operationName, e.getClass().getSimpleName());
                    throw e;
                }
                
                // 检查是否还能继续重试
                if (!retryPolicy.canRetry(retryCount)) {
                    log.error("操作失败，已达到最大重试次数 - 操作: {}, 重试次数: {}, 最后异常: {}", 
                             operationName, retryCount, e.getMessage());
                    throw e;
                }
                
                retryCount++;
                
                // 计算延迟时间
                long delay = retryPolicy.calculateDelay(retryCount);
                
                log.warn("操作失败，准备重试 - 操作: {}, 重试次数: {}/{}, 延迟: {}ms, 异常: {}", 
                        operationName, retryCount, retryPolicy.getMaxRetryCount(), delay, e.getMessage());
                
                // 调用重试回调
                if (retryCallback != null) {
                    RetryContext context = new RetryContext(retryCount, retryPolicy.getMaxRetryCount(), delay, e, operationName);
                    try {
                        retryCallback.accept(context);
                    } catch (Exception callbackException) {
                        log.warn("重试回调执行失败", callbackException);
                    }
                }
                
                // 等待延迟时间
                if (delay > 0) {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("重试延迟被中断");
                        throw new RuntimeException("重试被中断", ie);
                    }
                }
            }
        }
    }
    
    /**
     * 执行带重传的操作（异步版本）
     * 
     * @param operation 要执行的操作
     * @param operationName 操作名称
     * @param retryCallback 重试回调函数（可选）
     * @param <T> 返回值类型
     * @return 操作结果的Future
     */
    public <T> java.util.concurrent.CompletableFuture<T> executeAsync(
            Callable<T> operation, String operationName, Consumer<RetryContext> retryCallback) {
        
        return java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            try {
                return execute(operation, operationName, retryCallback);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 创建默认重传执行器
     * 
     * @return 默认重传执行器
     */
    public static RetryExecutor defaultExecutor() {
        return new RetryExecutor(RetryPolicy.defaultPolicy());
    }
    
    /**
     * 创建快速重传执行器
     * 
     * @return 快速重传执行器
     */
    public static RetryExecutor fastRetryExecutor() {
        return new RetryExecutor(RetryPolicy.fastRetryPolicy());
    }
    
    /**
     * 创建保守重传执行器
     * 
     * @return 保守重传执行器
     */
    public static RetryExecutor conservativeExecutor() {
        return new RetryExecutor(RetryPolicy.conservativeRetryPolicy());
    }
    
    /**
     * 获取重传策略
     * 
     * @return 重传策略
     */
    public RetryPolicy getRetryPolicy() {
        return retryPolicy;
    }
    
    /**
     * 可抛出异常的Runnable接口
     */
    @FunctionalInterface
    public interface RunnableWithException {
        void run() throws Exception;
    }
    
    /**
     * 重试上下文信息
     */
    public static class RetryContext {
        private final int currentRetryCount;
        private final int maxRetryCount;
        private final long delayMs;
        private final Exception lastException;
        private final String operationName;
        
        public RetryContext(int currentRetryCount, int maxRetryCount, long delayMs, 
                           Exception lastException, String operationName) {
            this.currentRetryCount = currentRetryCount;
            this.maxRetryCount = maxRetryCount;
            this.delayMs = delayMs;
            this.lastException = lastException;
            this.operationName = operationName;
        }
        
        public int getCurrentRetryCount() {
            return currentRetryCount;
        }
        
        public int getMaxRetryCount() {
            return maxRetryCount;
        }
        
        public long getDelayMs() {
            return delayMs;
        }
        
        public Exception getLastException() {
            return lastException;
        }
        
        public String getOperationName() {
            return operationName;
        }
        
        public double getProgressPercentage() {
            return maxRetryCount > 0 ? (double) currentRetryCount / maxRetryCount * 100.0 : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("RetryContext{operation='%s', retry=%d/%d, delay=%dms, exception='%s'}", 
                               operationName, currentRetryCount, maxRetryCount, delayMs, 
                               lastException != null ? lastException.getMessage() : "null");
        }
    }
}
