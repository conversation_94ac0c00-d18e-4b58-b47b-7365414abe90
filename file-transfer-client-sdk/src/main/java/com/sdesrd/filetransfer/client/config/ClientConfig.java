package com.sdesrd.filetransfer.client.config;

import lombok.Data;

/**
 * 客户端配置类
 */
@Data
public class ClientConfig {
    
    /**
     * 认证配置（必填）
     */
    private ClientAuthConfig auth = new ClientAuthConfig();
    
    /**
     * 分块大小（字节），默认2MB
     */
    private long chunkSize = 2 * 1024 * 1024L;
    
    /**
     * 连接超时时间（秒），默认30秒
     */
    private long connectTimeoutSeconds = 30;
    
    /**
     * 读取超时时间（秒），默认60秒
     */
    private long readTimeoutSeconds = 60;
    
    /**
     * 写入超时时间（秒），默认60秒
     */
    private long writeTimeoutSeconds = 60;
    
    /**
     * 最大并发传输数，默认3
     */
    private int maxConcurrentTransfers = 3;
    
    /**
     * 最大空闲连接数，默认5
     */
    private int maxIdleConnections = 5;
    
    /**
     * 连接保活时间（分钟），默认5分钟
     */
    private long keepAliveDurationMinutes = 5;
    
    /**
     * 重试次数，默认3次
     */
    private int retryCount = 3;

    /**
     * 重试间隔（毫秒），默认1秒
     */
    private long retryIntervalMs = 1000;

    /**
     * 重传策略配置
     */
    private com.sdesrd.filetransfer.client.retry.RetryPolicy retryPolicy;

    /**
     * 是否启用并发分片传输，默认false
     */
    private boolean concurrentChunkTransferEnabled = false;

    /**
     * 并发分片传输的最大线程数，默认3
     */
    private int maxConcurrentChunks = 3;

    /**
     * 获取服务器URL
     *
     * @return 服务器基础URL
     */
    public String getServerUrl() {
        validateConfig();
        return auth.getServerUrl();
    }

    /**
     * 验证完整配置
     *
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public void validateConfig() {
        if (auth == null) {
            throw new IllegalStateException("认证配置不能为空");
        }

        // 验证认证配置
        auth.validate();

        // 验证其他配置
        if (chunkSize <= 0) {
            throw new IllegalStateException("分片大小必须大于0");
        }
        if (retryCount < 0) {
            throw new IllegalStateException("重试次数不能为负数");
        }
        if (retryIntervalMs < 0) {
            throw new IllegalStateException("重试间隔不能为负数");
        }
        if (maxConcurrentTransfers <= 0) {
            throw new IllegalStateException("最大并发传输数必须大于0");
        }
        if (maxConcurrentChunks <= 0) {
            throw new IllegalStateException("最大并发分片数必须大于0");
        }
    }

    /**
     * 获取重传策略，如果未设置则返回默认策略
     *
     * @return 重传策略
     */
    public com.sdesrd.filetransfer.client.retry.RetryPolicy getRetryPolicy() {
        if (retryPolicy == null) {
            // 基于现有配置创建默认重传策略
            com.sdesrd.filetransfer.client.retry.RetryPolicy defaultPolicy =
                com.sdesrd.filetransfer.client.retry.RetryPolicy.defaultPolicy();
            defaultPolicy.setMaxRetryCount(retryCount);
            defaultPolicy.setBaseDelayMs(retryIntervalMs);
            return defaultPolicy;
        }
        return retryPolicy;
    }
} 