package com.sdesrd.filetransfer.client;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.ApiResult;
import com.sdesrd.filetransfer.client.dto.ChunkedDownloadRequest;
import com.sdesrd.filetransfer.client.dto.ChunkedDownloadResult;
import com.sdesrd.filetransfer.client.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.client.dto.S3GetObjectResult;
import com.sdesrd.filetransfer.client.dto.S3ListObjectsResponse;
import com.sdesrd.filetransfer.client.dto.S3ListObjectsResult;
import com.sdesrd.filetransfer.client.dto.S3ObjectInfo;
import com.sdesrd.filetransfer.client.dto.S3PutObjectRequest;
import com.sdesrd.filetransfer.client.dto.S3PutObjectResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.AuthUtils;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * S3风格的文件传输客户端
 * 提供类似AWS S3 SDK的操作接口
 */
@Slf4j
public class S3FileTransferClient implements Closeable {
    
    private final ClientConfig config;
    private final OkHttpClient httpClient;
    
    public S3FileTransferClient(ClientConfig config) {
        this.config = config;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeoutSeconds(), TimeUnit.SECONDS)
                .readTimeout(config.getReadTimeoutSeconds(), TimeUnit.SECONDS)
                .writeTimeout(config.getWriteTimeoutSeconds(), TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 上传对象（同步）
     */
    public S3PutObjectResult putObject(String bucket, String key, String filePath, TransferListener listener) throws FileTransferException {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileTransferException("文件不存在: " + filePath);
            }
            return putObject(bucket, key, file, listener);
        } catch (Exception e) {
            log.error("上传对象失败 - bucket: {}, key: {}, file: {}", bucket, key, filePath, e);
            throw new FileTransferException("上传对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 上传对象（同步）
     */
    public S3PutObjectResult putObject(String bucket, String key, File file, TransferListener listener) throws FileTransferException {
        try {
            log.info("开始上传对象 - bucket: {}, key: {}, file: {}", bucket, key, file.getName());
            
            // 创建上传请求
            S3PutObjectRequest request = new S3PutObjectRequest();
            request.setBucket(bucket);
            request.setKey(key);
            request.setFileName(file.getName());
            request.setFileSize(file.length());
            request.setContentMd5(FileUtils.calculateMD5(file)); // 用于传输完整性校验
            request.setOverwrite(true); // 默认允许覆盖
            
            log.info("上传请求已创建 - transferId即将初始化");
            
            // 初始化上传
            FileUploadInitResponse initResponse = initS3Upload(request);
            String transferId = initResponse.getTransferId();
            
            log.info("上传初始化完成 - transferId: {}", transferId);
            
            // 调用监听器的开始方法
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setTransferId(transferId);
                progress.setFileName(file.getName());
                progress.setTotalSize(file.length());
                progress.setTransferredSize(0L);
                progress.setProgress(0.0);
                listener.onStart(progress);
            }
            
            // 分块上传
            int chunkSize = initResponse.getChunkSize().intValue();
            int totalChunks = (int) Math.ceil((double) file.length() / chunkSize);
            
            log.info("开始分块上传 - chunkSize: {}, totalChunks: {}", chunkSize, totalChunks);
            
            byte[] buffer = new byte[chunkSize];
            try (FileInputStream fis = new FileInputStream(file)) {
                for (int i = 0; i < totalChunks; i++) {
                    int bytesRead = fis.read(buffer);
                    byte[] chunkData = bytesRead == chunkSize ? buffer : Arrays.copyOf(buffer, bytesRead);
                    
                    uploadChunk(transferId, i, chunkData);
                    log.debug("上传分块完成 - index: {}, size: {}", i, chunkData.length);
                    
                    if (listener != null) {
                        long transferredSize = Math.min((long) (i + 1) * chunkSize, file.length());
                        TransferProgress progress = new TransferProgress();
                        progress.setTransferId(transferId);
                        progress.setFileName(file.getName());
                        progress.setTotalSize(file.length());
                        progress.setTransferredSize(transferredSize);
                        progress.setProgress((double) transferredSize / file.length() * 100);
                        listener.onProgress(progress);
                    }
                }
            }
            
            log.info("所有分块上传完成，开始完成上传 - transferId: {}", transferId);
            
            // 完成上传
            String result = completeS3Upload(transferId, bucket, key);
            log.info("分块上传完成 - result: {}", result);
            
            S3PutObjectResult putResult = new S3PutObjectResult();
            putResult.setKey(key);
            putResult.setEtag(transferId);
            putResult.setSuccess(true);
            
            log.info("分块上传结果已创建 - success: {}", putResult.isSuccess());
            
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setTransferId(transferId);
                progress.setFileName(file.getName());
                progress.setTotalSize(file.length());
                progress.setTransferredSize(file.length());
                progress.setProgress(100.0);
                listener.onCompleted(progress);
            }
            
            return putResult;
            
        } catch (Exception e) {
            log.error("上传对象失败 - bucket: {}, key: {}, file: {}", bucket, key, file.getAbsolutePath(), e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(key);
                listener.onError(progress, e);
            }
            throw new FileTransferException("上传对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步上传对象
     */
    public CompletableFuture<S3PutObjectResult> putObjectAsync(String bucket, String key, String filePath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return putObject(bucket, key, filePath, listener);
            } catch (FileTransferException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 获取对象（下载）
     */
    public S3GetObjectResult getObject(String bucket, String key, String localPath, TransferListener listener) throws FileTransferException {
        try {
            log.info("下载对象 - bucket: {}, key: {}, localPath: {}", bucket, key, localPath);
            
            // 对key进行URL编码，处理路径分隔符
            String encodedKey = java.net.URLEncoder.encode(key, "UTF-8").replace("%2F", "/");
            String url = config.getServerUrl() + "/filetransfer/api/s3/" + bucket + "/" + encodedKey;
            log.debug("下载URL: {}", url);
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("下载失败: " + response.code() + " - " + errorBody);
                }
                
                ResponseBody body = response.body();
                if (body == null) {
                    throw new FileTransferException("响应体为空");
                }
                
                long totalSize = body.contentLength();
                
                // 确保目标目录存在
                File localFile = new File(localPath);
                File parentDir = localFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                // 下载文件
                try (InputStream inputStream = body.byteStream();
                     FileOutputStream outputStream = new FileOutputStream(localFile)) {
                    
                    byte[] buffer = new byte[8192];
                    long downloadedSize = 0;
                    int bytesRead;
                    
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        downloadedSize += bytesRead;
                        
                        if (listener != null) {
                            TransferProgress progress = new TransferProgress();
                            progress.setFileName(localFile.getName());
                            progress.setTotalSize(totalSize);
                            progress.setTransferredSize(downloadedSize);
                            progress.setProgress(totalSize > 0 ? (double) downloadedSize / totalSize * 100 : 100.0);
                            listener.onProgress(progress);
                        }
                    }
                }
                
                // 创建下载结果
                S3GetObjectResult result = new S3GetObjectResult();
                result.setKey(key);
                result.setLocalPath(localPath);
                result.setFileSize(totalSize);
                result.setSuccess(true);
                
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(localFile.getName());
                    progress.setTotalSize(totalSize);
                    progress.setTransferredSize(totalSize);
                    progress.setProgress(100.0);
                    listener.onCompleted(progress);
                }
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("下载对象失败 - bucket: {}, key: {}", bucket, key, e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(key);
                listener.onError(progress, e);
            }
            throw new FileTransferException("下载对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步获取对象（下载）
     */
    public CompletableFuture<S3GetObjectResult> getObjectAsync(String bucket, String key, String localPath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getObject(bucket, key, localPath, listener);
            } catch (FileTransferException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 分片下载对象（同步）
     * 支持断点续传和并发下载
     */
    public ChunkedDownloadResult chunkedDownload(ChunkedDownloadRequest request, TransferListener listener) throws FileTransferException {
        try {
            request.validate();

            log.info("开始分片下载 - bucket: {}, key: {}, localPath: {}",
                    request.getBucket(), request.getKey(), request.getLocalPath());

            long startTime = System.currentTimeMillis();

            // 初始化分片下载
            String downloadId = initChunkedDownload(request);
            log.info("分片下载初始化完成 - downloadId: {}", downloadId);

            // 获取下载状态
            Map<String, Object> downloadStatus = getDownloadStatus(downloadId);
            Integer totalChunks = (Integer) downloadStatus.get("totalChunks");
            Long fileSize = ((Number) downloadStatus.get("fileSize")).longValue();

            // 创建本地文件
            File localFile = new File(request.getLocalPath());
            if (!request.isOverwrite() && localFile.exists()) {
                throw new FileTransferException("本地文件已存在: " + request.getLocalPath());
            }

            // 确保父目录存在
            File parentDir = localFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 创建空文件
            try (RandomAccessFile raf = new RandomAccessFile(localFile, "rw")) {
                raf.setLength(fileSize);
            }

            // 通知开始下载
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setTransferId(downloadId);
                progress.setFileName(request.getKey());
                progress.setTotalSize(fileSize);
                progress.setTransferredSize(0L);
                progress.setProgress(0.0);
                progress.setTotalChunks(totalChunks);
                progress.setCompletedChunks(0);
                listener.onStart(progress);
            }

            // 执行分片下载
            boolean success = performChunkedDownload(downloadId, localFile, totalChunks, request, listener);

            long endTime = System.currentTimeMillis();

            // 创建结果
            ChunkedDownloadResult result = new ChunkedDownloadResult();
            result.setDownloadId(downloadId);
            result.setBucket(request.getBucket());
            result.setKey(request.getKey());
            result.setLocalPath(request.getLocalPath());
            result.setFileSize(fileSize);
            result.setTotalChunks(totalChunks);
            result.setStartTime(startTime);
            result.setEndTime(endTime);
            result.setSuccess(success);

            if (success) {
                result.setStatus(ChunkedDownloadResult.DownloadStatus.COMPLETED);
                result.setDownloadedChunks(totalChunks);

                // 通知完成
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setTransferId(downloadId);
                    progress.setFileName(request.getKey());
                    progress.setTotalSize(fileSize);
                    progress.setTransferredSize(fileSize);
                    progress.setProgress(100.0);
                    progress.setTotalChunks(totalChunks);
                    progress.setCompletedChunks(totalChunks);
                    progress.setCompleted(true);
                    listener.onCompleted(progress);
                }
            } else {
                result.setStatus(ChunkedDownloadResult.DownloadStatus.FAILED);
                result.setErrorMessage("分片下载失败");
            }

            result.calculateProgress();
            result.calculateDuration();
            result.calculateAverageSpeed();

            return result;

        } catch (Exception e) {
            log.error("分片下载失败 - bucket: {}, key: {}", request.getBucket(), request.getKey(), e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(request.getKey());
                listener.onError(progress, e);
            }
            throw new FileTransferException("分片下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步分片下载对象
     */
    public CompletableFuture<ChunkedDownloadResult> chunkedDownloadAsync(ChunkedDownloadRequest request, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return chunkedDownload(request, listener);
            } catch (FileTransferException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 列出对象
     */
    public S3ListObjectsResult listObjects(String bucket, String prefix, String delimiter, Integer maxKeys) throws FileTransferException {
        try {
            StringBuilder urlBuilder = new StringBuilder(config.getServerUrl() + "/filetransfer/api/s3/?bucket=" + java.net.URLEncoder.encode(bucket, "UTF-8"));
            
            if (prefix != null) {
                urlBuilder.append("&prefix=").append(java.net.URLEncoder.encode(prefix, "UTF-8"));
            }
            if (delimiter != null) {
                urlBuilder.append("&delimiter=").append(java.net.URLEncoder.encode(delimiter, "UTF-8"));
            }
            if (maxKeys != null) {
                urlBuilder.append("&maxKeys=").append(maxKeys);
            }
            
            String url = urlBuilder.toString();
            
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("列出对象失败: " + response.code() + " - " + errorBody);
                }
                
                String responseBody = response.body().string();
                ApiResult<S3ListObjectsResponse> apiResult = JSON.parseObject(responseBody, 
                        new TypeReference<ApiResult<S3ListObjectsResponse>>() {});
                
                if (!apiResult.isSuccess()) {
                    throw new FileTransferException("列出对象失败: " + apiResult.getMessage());
                }
                
                S3ListObjectsResult result = new S3ListObjectsResult();
                result.setListObjectsResponse(apiResult.getData());
                result.setSuccess(true);
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("列出对象失败 - bucket: {}, prefix: {}", bucket, prefix, e);
            throw new FileTransferException("列出对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除对象
     */
    public boolean deleteObject(String bucket, String key) throws FileTransferException {
        try {
            // 对key进行URL编码，处理路径分隔符
            String encodedKey = java.net.URLEncoder.encode(key, "UTF-8").replace("%2F", "/");
            String url = config.getServerUrl() + "/filetransfer/api/s3/" + bucket + "/" + encodedKey;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .delete();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    if (response.code() == 404) {
                        return false; // 文件不存在
                    }
                    throw new FileTransferException("删除对象失败: " + response.code() + " - " + errorBody);
                }
                
                return true;
            }
            
        } catch (Exception e) {
            log.error("删除对象失败 - bucket: {}, key: {}", bucket, key, e);
            throw new FileTransferException("删除对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取对象信息
     */
    public S3ObjectInfo getObjectInfo(String bucket, String key) throws FileTransferException {
        try {
            // 对key进行URL编码，处理路径分隔符
            String encodedKey = java.net.URLEncoder.encode(key, "UTF-8").replace("%2F", "/");
            String url = config.getServerUrl() + "/filetransfer/api/s3/info/" + bucket + "/" + encodedKey;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    if (response.code() == 404) {
                        return null; // 文件不存在
                    }
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("获取对象信息失败: " + response.code() + " - " + errorBody);
                }
                
                String responseBody = response.body().string();
                ApiResult<S3ObjectInfo> apiResult = JSON.parseObject(responseBody, 
                        new TypeReference<ApiResult<S3ObjectInfo>>() {});
                
                if (!apiResult.isSuccess()) {
                    throw new FileTransferException("获取对象信息失败: " + apiResult.getMessage());
                }
                
                return apiResult.getData();
            }
            
        } catch (Exception e) {
            log.error("获取对象信息失败 - bucket: {}, key: {}", bucket, key, e);
            throw new FileTransferException("获取对象信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 初始化S3上传
     */
    private FileUploadInitResponse initS3Upload(S3PutObjectRequest request) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/init";
        String json = JSON.toJSONString(request);
        
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request httpRequest = requestBuilder.build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("初始化上传失败: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            ApiResult<FileUploadInitResponse> apiResult = JSON.parseObject(responseBody, 
                    new TypeReference<ApiResult<FileUploadInitResponse>>() {});
            
            if (!apiResult.isSuccess()) {
                throw new IOException("初始化上传失败: " + apiResult.getMessage());
            }
            
            return apiResult.getData();
        }
    }
    
    /**
     * 上传分块
     */
    private void uploadChunk(String transferId, int chunkIndex, byte[] chunkData) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/chunk";
        
        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("transferId", transferId)
                .addFormDataPart("chunkIndex", String.valueOf(chunkIndex))
                .addFormDataPart("chunk", "chunk_" + chunkIndex, 
                        RequestBody.create(MediaType.parse("application/octet-stream"), chunkData))
                .build();
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody);
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("上传分块失败: " + response.code() + " - " + errorBody);
            }
        }
    }
    
    /**
     * 完成S3上传
     */
    private String completeS3Upload(String transferId, String bucket, String key) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/complete?transferId=" + transferId 
                + "&bucket=" + java.net.URLEncoder.encode(bucket, "UTF-8")
                + "&key=" + java.net.URLEncoder.encode(key, "UTF-8");
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json"), ""));
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("完成上传失败: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            ApiResult<String> apiResult = JSON.parseObject(responseBody, 
                    new TypeReference<ApiResult<String>>() {});
            
            if (!apiResult.isSuccess()) {
                throw new IOException("完成上传失败: " + apiResult.getMessage());
            }
            
            return apiResult.getData();
        }
    }
    
    // === 分片下载辅助方法 ===

    /**
     * 初始化分片下载
     */
    private String initChunkedDownload(ChunkedDownloadRequest request) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/download/init";

        FormBody.Builder formBuilder = new FormBody.Builder()
                .add("bucket", request.getBucket())
                .add("key", request.getKey());

        if (request.getChunkSize() != null) {
            formBuilder.add("chunkSize", String.valueOf(request.getChunkSize()));
        }

        RequestBody requestBody = formBuilder.build();

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());

        Request httpRequest = requestBuilder.build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("初始化分片下载失败: " + response.code() + " - " + errorBody);
            }

            String responseBody = response.body().string();
            ApiResult<Map<String, Object>> apiResult = JSON.parseObject(responseBody,
                    new TypeReference<ApiResult<Map<String, Object>>>() {});

            if (!apiResult.isSuccess()) {
                throw new IOException("初始化分片下载失败: " + apiResult.getMessage());
            }

            Map<String, Object> data = apiResult.getData();
            return (String) data.get("downloadId");
        }
    }

    /**
     * 获取下载状态
     */
    private Map<String, Object> getDownloadStatus(String downloadId) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/download/status/" + downloadId;

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .get();

        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());

        Request request = requestBuilder.build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("获取下载状态失败: " + response.code() + " - " + errorBody);
            }

            String responseBody = response.body().string();
            ApiResult<Map<String, Object>> apiResult = JSON.parseObject(responseBody,
                    new TypeReference<ApiResult<Map<String, Object>>>() {});

            if (!apiResult.isSuccess()) {
                throw new IOException("获取下载状态失败: " + apiResult.getMessage());
            }

            return apiResult.getData();
        }
    }

    /**
     * 执行分片下载
     */
    private boolean performChunkedDownload(String downloadId, File localFile, int totalChunks,
                                         ChunkedDownloadRequest request, TransferListener listener) {
        try {
            if (config.isConcurrentChunkTransferEnabled() && request.getMaxConcurrentChunks() > 1) {
                return performConcurrentChunkedDownload(downloadId, localFile, totalChunks, request, listener);
            } else {
                return performSequentialChunkedDownload(downloadId, localFile, totalChunks, request, listener);
            }
        } catch (Exception e) {
            log.error("分片下载执行失败", e);
            return false;
        }
    }

    /**
     * 顺序分片下载
     */
    private boolean performSequentialChunkedDownload(String downloadId, File localFile, int totalChunks,
                                                    ChunkedDownloadRequest request, TransferListener listener) {
        try (RandomAccessFile raf = new RandomAccessFile(localFile, "rw")) {
            for (int i = 0; i < totalChunks; i++) {
                boolean success = downloadSingleChunk(downloadId, i, raf, listener);
                if (!success) {
                    log.error("下载分块失败: {}", i);
                    return false;
                }

                // 更新进度
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setTransferId(downloadId);
                    progress.setFileName(request.getKey());
                    progress.setCompletedChunks(i + 1);
                    progress.setTotalChunks(totalChunks);
                    progress.setProgress((double) (i + 1) / totalChunks * 100.0);
                    listener.onProgress(progress);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("顺序分片下载失败", e);
            return false;
        }
    }

    /**
     * 并发分片下载
     */
    private boolean performConcurrentChunkedDownload(String downloadId, File localFile, int totalChunks,
                                                   ChunkedDownloadRequest request, TransferListener listener) {
        // TODO: 实现并发下载逻辑
        // 这里先使用顺序下载作为fallback
        return performSequentialChunkedDownload(downloadId, localFile, totalChunks, request, listener);
    }

    /**
     * 下载单个分块
     */
    private boolean downloadSingleChunk(String downloadId, int chunkIndex, RandomAccessFile raf, TransferListener listener) {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/s3/download/chunk" +
                        "?downloadId=" + downloadId + "&chunkIndex=" + chunkIndex;

            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();

            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());

            Request request = requestBuilder.build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("下载分块失败: chunk={}, code={}", chunkIndex, response.code());
                    return false;
                }

                // 获取分块信息
                String startPosHeader = response.header("X-Start-Position");
                if (startPosHeader != null) {
                    long startPosition = Long.parseLong(startPosHeader);
                    raf.seek(startPosition);
                }

                // 写入分块数据
                try (InputStream inputStream = response.body().byteStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        raf.write(buffer, 0, bytesRead);
                    }
                }

                return true;
            }
        } catch (Exception e) {
            log.error("下载分块异常: chunk={}", chunkIndex, e);
            return false;
        }
    }

    @Override
    public void close() throws IOException {
        // OkHttpClient会自动管理连接池，一般不需要手动关闭
        // 如果需要立即释放资源，可以关闭连接池
    }
}