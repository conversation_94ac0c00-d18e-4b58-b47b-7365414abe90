package com.sdesrd.filetransfer.client.dto;

import java.util.List;

import lombok.Data;

/**
 * 分片下载结果
 */
@Data
public class ChunkedDownloadResult {
    
    /**
     * 下载任务ID
     */
    private String downloadId;
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 对象key
     */
    private String key;
    
    /**
     * 本地文件路径
     */
    private String localPath;
    
    /**
     * 文件总大小
     */
    private Long fileSize;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已下载分块数
     */
    private Integer downloadedChunks;
    
    /**
     * 下载状态
     */
    private DownloadStatus status;
    
    /**
     * 下载进度百分比（0-100）
     */
    private Double progress;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 下载开始时间
     */
    private Long startTime;
    
    /**
     * 下载结束时间
     */
    private Long endTime;
    
    /**
     * 下载耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 平均下载速度（字节/秒）
     */
    private Double averageSpeed;
    
    /**
     * 失败的分块列表
     */
    private List<Integer> failedChunks;
    
    /**
     * 下载状态枚举
     */
    public enum DownloadStatus {
        /** 初始化 */
        INITIALIZED,
        /** 下载中 */
        DOWNLOADING,
        /** 已完成 */
        COMPLETED,
        /** 失败 */
        FAILED,
        /** 已暂停 */
        PAUSED,
        /** 已取消 */
        CANCELLED
    }
    
    /**
     * 计算下载进度
     */
    public void calculateProgress() {
        if (totalChunks != null && totalChunks > 0 && downloadedChunks != null) {
            this.progress = (double) downloadedChunks / totalChunks * 100.0;
        } else {
            this.progress = 0.0;
        }
    }
    
    /**
     * 计算下载耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
    }
    
    /**
     * 计算平均下载速度
     */
    public void calculateAverageSpeed() {
        if (fileSize != null && duration != null && duration > 0) {
            // 字节/秒
            this.averageSpeed = (double) fileSize / (duration / 1000.0);
        }
    }
    
    /**
     * 检查是否完成
     */
    public boolean isCompleted() {
        return DownloadStatus.COMPLETED == status;
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return DownloadStatus.FAILED == status;
    }
    
    /**
     * 检查是否暂停
     */
    public boolean isPaused() {
        return DownloadStatus.PAUSED == status;
    }
    
    /**
     * 检查是否取消
     */
    public boolean isCancelled() {
        return DownloadStatus.CANCELLED == status;
    }
    
    /**
     * 创建成功结果
     */
    public static ChunkedDownloadResult success(String downloadId, String bucket, String key, String localPath) {
        ChunkedDownloadResult result = new ChunkedDownloadResult();
        result.setDownloadId(downloadId);
        result.setBucket(bucket);
        result.setKey(key);
        result.setLocalPath(localPath);
        result.setStatus(DownloadStatus.COMPLETED);
        result.setSuccess(true);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static ChunkedDownloadResult failure(String downloadId, String bucket, String key, String errorMessage) {
        ChunkedDownloadResult result = new ChunkedDownloadResult();
        result.setDownloadId(downloadId);
        result.setBucket(bucket);
        result.setKey(key);
        result.setStatus(DownloadStatus.FAILED);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
