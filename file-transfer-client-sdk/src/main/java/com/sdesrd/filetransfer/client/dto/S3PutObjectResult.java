package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * S3上传对象结果DTO
 */
@Data
public class S3PutObjectResult {
    
    /**
     * 对象键
     */
    private String key;
    
    /**
     * ETag值
     */
    private String etag;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 版本ID（如果启用版本控制）
     */
    private String versionId;
    
    /**
     * 便捷方法：判断操作是否成功
     * 
     * @return 如果操作成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }
} 