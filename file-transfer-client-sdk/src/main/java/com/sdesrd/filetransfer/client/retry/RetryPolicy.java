package com.sdesrd.filetransfer.client.retry;

import java.util.concurrent.ThreadLocalRandom;

import lombok.Data;

/**
 * 重传策略配置类
 * 定义重传的各种策略和参数
 */
@Data
public class RetryPolicy {
    
    /** 默认最大重试次数 */
    public static final int DEFAULT_MAX_RETRY_COUNT = 3;
    
    /** 默认基础延迟时间（毫秒） */
    public static final long DEFAULT_BASE_DELAY_MS = 1000L;
    
    /** 默认最大延迟时间（毫秒） */
    public static final long DEFAULT_MAX_DELAY_MS = 30000L;
    
    /** 默认退避倍数 */
    public static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    
    /**
     * 最大重试次数
     */
    private int maxRetryCount = DEFAULT_MAX_RETRY_COUNT;
    
    /**
     * 基础延迟时间（毫秒）
     */
    private long baseDelayMs = DEFAULT_BASE_DELAY_MS;
    
    /**
     * 最大延迟时间（毫秒）
     */
    private long maxDelayMs = DEFAULT_MAX_DELAY_MS;
    
    /**
     * 退避策略
     */
    private BackoffStrategy backoffStrategy = BackoffStrategy.EXPONENTIAL;
    
    /**
     * 退避倍数（用于指数退避）
     */
    private double backoffMultiplier = DEFAULT_BACKOFF_MULTIPLIER;
    
    /**
     * 是否启用抖动（避免惊群效应）
     */
    private boolean jitterEnabled = true;
    
    /**
     * 抖动因子（0.0-1.0）
     */
    private double jitterFactor = 0.1;
    
    /**
     * 可重试的异常类型
     */
    private Class<? extends Exception>[] retryableExceptions = new Class[]{
        java.io.IOException.class,
        java.net.SocketTimeoutException.class,
        java.net.ConnectException.class,
        java.net.SocketException.class
    };
    
    /**
     * 退避策略枚举
     */
    public enum BackoffStrategy {
        /** 固定延迟 */
        FIXED,
        /** 线性退避 */
        LINEAR,
        /** 指数退避 */
        EXPONENTIAL
    }
    
    /**
     * 创建默认重传策略
     * 
     * @return 默认重传策略
     */
    public static RetryPolicy defaultPolicy() {
        return new RetryPolicy();
    }
    
    /**
     * 创建快速重传策略（适用于网络抖动）
     * 
     * @return 快速重传策略
     */
    public static RetryPolicy fastRetryPolicy() {
        RetryPolicy policy = new RetryPolicy();
        policy.setMaxRetryCount(5);
        policy.setBaseDelayMs(500L);
        policy.setMaxDelayMs(5000L);
        policy.setBackoffStrategy(BackoffStrategy.LINEAR);
        return policy;
    }
    
    /**
     * 创建保守重传策略（适用于服务器压力大的情况）
     * 
     * @return 保守重传策略
     */
    public static RetryPolicy conservativeRetryPolicy() {
        RetryPolicy policy = new RetryPolicy();
        policy.setMaxRetryCount(2);
        policy.setBaseDelayMs(2000L);
        policy.setMaxDelayMs(60000L);
        policy.setBackoffStrategy(BackoffStrategy.EXPONENTIAL);
        policy.setBackoffMultiplier(3.0);
        return policy;
    }
    
    /**
     * 计算重试延迟时间
     * 
     * @param retryCount 当前重试次数（从1开始）
     * @return 延迟时间（毫秒）
     */
    public long calculateDelay(int retryCount) {
        if (retryCount <= 0) {
            return 0L;
        }
        
        long delay;
        
        switch (backoffStrategy) {
            case FIXED:
                delay = baseDelayMs;
                break;
                
            case LINEAR:
                delay = baseDelayMs * retryCount;
                break;
                
            case EXPONENTIAL:
                delay = (long) (baseDelayMs * Math.pow(backoffMultiplier, retryCount - 1));
                break;
                
            default:
                delay = baseDelayMs;
                break;
        }
        
        // 限制最大延迟时间
        delay = Math.min(delay, maxDelayMs);
        
        // 应用抖动
        if (jitterEnabled) {
            delay = applyJitter(delay);
        }
        
        return delay;
    }
    
    /**
     * 检查异常是否可重试
     * 
     * @param exception 异常
     * @return 是否可重试
     */
    public boolean isRetryableException(Exception exception) {
        if (exception == null) {
            return false;
        }
        
        for (Class<? extends Exception> retryableType : retryableExceptions) {
            if (retryableType.isAssignableFrom(exception.getClass())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否可以继续重试
     * 
     * @param retryCount 当前重试次数
     * @return 是否可以继续重试
     */
    public boolean canRetry(int retryCount) {
        return retryCount < maxRetryCount;
    }
    
    /**
     * 应用抖动
     * 
     * @param delay 原始延迟时间
     * @return 应用抖动后的延迟时间
     */
    private long applyJitter(long delay) {
        if (jitterFactor <= 0.0) {
            return delay;
        }
        
        // 计算抖动范围
        long jitterRange = (long) (delay * jitterFactor);
        
        // 生成随机抖动值（-jitterRange 到 +jitterRange）
        long jitter = ThreadLocalRandom.current().nextLong(-jitterRange, jitterRange + 1);
        
        // 确保延迟时间不为负数
        return Math.max(0L, delay + jitter);
    }
    
    /**
     * 设置可重试的异常类型
     * 
     * @param exceptions 异常类型数组
     */
    @SafeVarargs
    public final void setRetryableExceptions(Class<? extends Exception>... exceptions) {
        this.retryableExceptions = exceptions;
    }
    
    /**
     * 添加可重试的异常类型
     * 
     * @param exceptionType 异常类型
     */
    public void addRetryableException(Class<? extends Exception> exceptionType) {
        Class<? extends Exception>[] newExceptions = new Class[retryableExceptions.length + 1];
        System.arraycopy(retryableExceptions, 0, newExceptions, 0, retryableExceptions.length);
        newExceptions[retryableExceptions.length] = exceptionType;
        this.retryableExceptions = newExceptions;
    }
    
    /**
     * 验证配置参数的有效性
     * 
     * @throws IllegalArgumentException 如果配置参数无效
     */
    public void validate() {
        if (maxRetryCount < 0) {
            throw new IllegalArgumentException("最大重试次数不能为负数");
        }
        
        if (baseDelayMs < 0) {
            throw new IllegalArgumentException("基础延迟时间不能为负数");
        }
        
        if (maxDelayMs < baseDelayMs) {
            throw new IllegalArgumentException("最大延迟时间不能小于基础延迟时间");
        }
        
        if (backoffMultiplier <= 0) {
            throw new IllegalArgumentException("退避倍数必须大于0");
        }
        
        if (jitterFactor < 0.0 || jitterFactor > 1.0) {
            throw new IllegalArgumentException("抖动因子必须在0.0-1.0之间");
        }
    }
    
    @Override
    public String toString() {
        return String.format("RetryPolicy{maxRetryCount=%d, baseDelayMs=%d, maxDelayMs=%d, " +
                           "backoffStrategy=%s, backoffMultiplier=%.2f, jitterEnabled=%s, jitterFactor=%.2f}",
                           maxRetryCount, baseDelayMs, maxDelayMs, backoffStrategy, 
                           backoffMultiplier, jitterEnabled, jitterFactor);
    }
}
