package com.sdesrd.filetransfer.client.retry;

import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 重传执行器测试
 */
@DisplayName("重传执行器测试")
class RetryExecutorTest {
    
    private RetryExecutor retryExecutor;
    private RetryPolicy retryPolicy;
    
    @BeforeEach
    void setUp() {
        retryPolicy = new RetryPolicy();
        retryPolicy.setMaxRetryCount(3);
        retryPolicy.setBaseDelayMs(100L);
        retryPolicy.setMaxDelayMs(1000L);
        retryPolicy.setBackoffStrategy(RetryPolicy.BackoffStrategy.FIXED);
        retryPolicy.setJitterEnabled(false); // 禁用抖动以便测试
        
        retryExecutor = new RetryExecutor(retryPolicy);
    }
    
    @Test
    @DisplayName("执行成功 - 无需重试")
    void testExecute_Success_NoRetry() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When
        String result = retryExecutor.execute(() -> {
            callCount.incrementAndGet();
            return "success";
        }, "test-operation");
        
        // Then
        assertEquals("success", result);
        assertEquals(1, callCount.get());
    }
    
    @Test
    @DisplayName("执行成功 - 重试后成功")
    void testExecute_Success_AfterRetry() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When
        String result = retryExecutor.execute(() -> {
            int count = callCount.incrementAndGet();
            if (count < 3) {
                throw new IOException("模拟网络错误");
            }
            return "success";
        }, "test-operation");
        
        // Then
        assertEquals("success", result);
        assertEquals(3, callCount.get());
    }
    
    @Test
    @DisplayName("执行失败 - 达到最大重试次数")
    void testExecute_Failed_MaxRetryReached() {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When & Then
        IOException exception = assertThrows(IOException.class, () -> {
            retryExecutor.execute(() -> {
                callCount.incrementAndGet();
                throw new IOException("持续失败");
            }, "test-operation");
        });
        
        assertEquals("持续失败", exception.getMessage());
        assertEquals(4, callCount.get()); // 1次初始调用 + 3次重试
    }
    
    @Test
    @DisplayName("执行失败 - 不可重试的异常")
    void testExecute_Failed_NonRetryableException() {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            retryExecutor.execute(() -> {
                callCount.incrementAndGet();
                throw new IllegalArgumentException("参数错误");
            }, "test-operation");
        });
        
        assertEquals("参数错误", exception.getMessage());
        assertEquals(1, callCount.get()); // 只调用1次，不重试
    }
    
    @Test
    @DisplayName("执行Runnable - 成功")
    void testExecute_Runnable_Success() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When
        retryExecutor.execute(() -> {
            callCount.incrementAndGet();
        }, "test-runnable");
        
        // Then
        assertEquals(1, callCount.get());
    }
    
    @Test
    @DisplayName("执行Runnable - 重试后成功")
    void testExecute_Runnable_RetrySuccess() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When
        retryExecutor.execute(() -> {
            int count = callCount.incrementAndGet();
            if (count < 2) {
                throw new SocketTimeoutException("超时");
            }
        }, "test-runnable");
        
        // Then
        assertEquals(2, callCount.get());
    }
    
    @Test
    @DisplayName("重试回调测试")
    void testExecute_WithRetryCallback() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        AtomicInteger callbackCount = new AtomicInteger(0);
        
        // When
        String result = retryExecutor.execute(() -> {
            int count = callCount.incrementAndGet();
            if (count < 3) {
                throw new IOException("模拟错误");
            }
            return "success";
        }, "test-operation", (context) -> {
            callbackCount.incrementAndGet();
            assertNotNull(context);
            assertTrue(context.getCurrentRetryCount() > 0);
            assertEquals("test-operation", context.getOperationName());
        });
        
        // Then
        assertEquals("success", result);
        assertEquals(3, callCount.get());
        assertEquals(2, callbackCount.get()); // 2次重试回调
    }
    
    @Test
    @DisplayName("异步执行测试")
    void testExecuteAsync() throws Exception {
        // Given
        AtomicInteger callCount = new AtomicInteger(0);
        
        // When
        String result = retryExecutor.executeAsync(() -> {
            callCount.incrementAndGet();
            return "async-success";
        }, "async-operation", null).get();
        
        // Then
        assertEquals("async-success", result);
        assertEquals(1, callCount.get());
    }
    
    @Test
    @DisplayName("默认执行器测试")
    void testDefaultExecutor() {
        // When
        RetryExecutor defaultExecutor = RetryExecutor.defaultExecutor();
        
        // Then
        assertNotNull(defaultExecutor);
        assertEquals(RetryPolicy.DEFAULT_MAX_RETRY_COUNT, defaultExecutor.getRetryPolicy().getMaxRetryCount());
    }
    
    @Test
    @DisplayName("快速重传执行器测试")
    void testFastRetryExecutor() {
        // When
        RetryExecutor fastExecutor = RetryExecutor.fastRetryExecutor();
        
        // Then
        assertNotNull(fastExecutor);
        assertEquals(5, fastExecutor.getRetryPolicy().getMaxRetryCount());
        assertEquals(500L, fastExecutor.getRetryPolicy().getBaseDelayMs());
    }
    
    @Test
    @DisplayName("保守重传执行器测试")
    void testConservativeExecutor() {
        // When
        RetryExecutor conservativeExecutor = RetryExecutor.conservativeExecutor();
        
        // Then
        assertNotNull(conservativeExecutor);
        assertEquals(2, conservativeExecutor.getRetryPolicy().getMaxRetryCount());
        assertEquals(2000L, conservativeExecutor.getRetryPolicy().getBaseDelayMs());
    }
    
    @Test
    @DisplayName("重试上下文测试")
    void testRetryContext() {
        // Given
        Exception testException = new IOException("测试异常");
        RetryExecutor.RetryContext context = new RetryExecutor.RetryContext(
                2, 3, 1000L, testException, "test-op");
        
        // Then
        assertEquals(2, context.getCurrentRetryCount());
        assertEquals(3, context.getMaxRetryCount());
        assertEquals(1000L, context.getDelayMs());
        assertEquals(testException, context.getLastException());
        assertEquals("test-op", context.getOperationName());
        assertEquals(66.67, context.getProgressPercentage(), 0.01);
        
        String contextStr = context.toString();
        assertTrue(contextStr.contains("test-op"));
        assertTrue(contextStr.contains("retry=2/3"));
        assertTrue(contextStr.contains("delay=1000ms"));
    }
    
    @Test
    @DisplayName("延迟计算测试")
    void testDelayCalculation() {
        // Given - 指数退避策略
        retryPolicy.setBackoffStrategy(RetryPolicy.BackoffStrategy.EXPONENTIAL);
        retryPolicy.setBackoffMultiplier(2.0);
        retryPolicy.setBaseDelayMs(100L);
        retryPolicy.setJitterEnabled(false);
        
        // When & Then
        assertEquals(100L, retryPolicy.calculateDelay(1));
        assertEquals(200L, retryPolicy.calculateDelay(2));
        assertEquals(400L, retryPolicy.calculateDelay(3));
    }
    
    @Test
    @DisplayName("可重试异常检查测试")
    void testRetryableExceptionCheck() {
        // Then
        assertTrue(retryPolicy.isRetryableException(new IOException()));
        assertTrue(retryPolicy.isRetryableException(new SocketTimeoutException()));
        assertFalse(retryPolicy.isRetryableException(new IllegalArgumentException()));
        assertFalse(retryPolicy.isRetryableException(null));
    }
    
    @Test
    @DisplayName("重试次数检查测试")
    void testCanRetryCheck() {
        // Then
        assertTrue(retryPolicy.canRetry(0));
        assertTrue(retryPolicy.canRetry(2));
        assertFalse(retryPolicy.canRetry(3));
        assertFalse(retryPolicy.canRetry(5));
    }
}
