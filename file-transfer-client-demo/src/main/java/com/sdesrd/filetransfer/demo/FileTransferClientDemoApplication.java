package com.sdesrd.filetransfer.demo;

import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.S3FileTransferClient;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 文件传输客户端演示应用
 * 
 * 这个应用演示了如何使用文件传输客户端SDK进行端到端测试
 * 包含完整的文件上传、下载、删除、错误处理和性能测试流程
 * 
 * 主要功能：
 * - 传统模式文件传输演示
 * - S3风格文件传输演示
 * - 文件上传测试（同步和异步）
 * - 文件下载测试（同步和异步）
 * - 文件信息查询测试
 * - 文件删除测试
 * - 错误处理测试
 * - 性能测试
 * - 大文件传输测试
 * - 并发传输测试
 */
@Slf4j
public class FileTransferClientDemoApplication {

    // 测试配置常量
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 49012;
    private static final String TEST_USER = "test-user";
    private static final String TEST_SECRET = "test-secret-key-2024";
    private static final String DEMO_USER = "demo";
    private static final String DEMO_SECRET = "demo-secret-key-2024";
    private static final String DEFAULT_BUCKET = "default";
    private static final String DEMO_BUCKET = "demo";
    
    // 测试文件配置
    private static final String TEST_FILE_NAME = "demo-test-file.txt";
    private static final String LARGE_TEST_FILE_NAME = "demo-large-test-file.dat";
    private static final String DOWNLOAD_DIR = "./download";
    
    // 测试超时配置
    private static final int OPERATION_TIMEOUT_SECONDS = 30;
    private static final int LARGE_FILE_TIMEOUT_SECONDS = 120;
    
    // 测试文件大小配置
    private static final int SMALL_FILE_SIZE_KB = 10;      // 10KB
    private static final int MEDIUM_FILE_SIZE_MB = 5;      // 5MB
    private static final int LARGE_FILE_SIZE_MB = 20;      // 20MB

    public static void main(String[] args) {
        log.info("===========================================");
        log.info("文件传输客户端演示应用启动");
        log.info("===========================================");
        
        FileTransferClientDemoApplication demo = new FileTransferClientDemoApplication();
        
        try {
            // 创建下载目录
            demo.createDownloadDirectory();
            
            // 等待服务器启动
            demo.waitForServerReady();
            
            // 执行传统模式演示
            log.info("\n" + createSeparator(50));
            log.info("开始传统模式文件传输演示");
            log.info(createSeparator(50));
            demo.runTraditionalModeDemo();

            // 执行S3风格演示
            log.info("\n" + createSeparator(50));
            log.info("开始S3风格文件传输演示");
            log.info(createSeparator(50));
            demo.runS3StyleDemo();

            // 执行性能测试
            log.info("\n" + createSeparator(50));
            log.info("开始性能测试演示");
            log.info(createSeparator(50));
            demo.runPerformanceTests();

            // 执行错误处理测试
            log.info("\n" + createSeparator(50));
            log.info("开始错误处理测试演示");
            log.info(createSeparator(50));
            demo.runErrorHandlingTests();

            log.info("\n" + createSeparator(50));
            log.info("所有演示测试完成！");
            log.info(createSeparator(50));
            
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
            System.exit(1);
        }
    }
    
    /**
     * 创建下载目录
     */
    private void createDownloadDirectory() {
        try {
            Path downloadPath = Paths.get(DOWNLOAD_DIR);
            if (!Files.exists(downloadPath)) {
                Files.createDirectories(downloadPath);
                log.info("创建下载目录：{}", DOWNLOAD_DIR);
            }
        } catch (IOException e) {
            log.error("创建下载目录失败", e);
            throw new RuntimeException("创建下载目录失败", e);
        }
    }
    
    /**
     * 等待服务器准备就绪
     */
    private void waitForServerReady() {
        log.info("等待服务器准备就绪...");

        int maxRetries = 30;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 使用简单的HTTP请求检查服务器健康状态
                java.net.URL url = new java.net.URL("http://" + SERVER_HOST + ":" + SERVER_PORT + "/actuator/health");
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    log.info("服务器已准备就绪");
                    return;
                }
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw new RuntimeException("等待服务器超时", e);
                }

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待被中断", ie);
                }
            }
        }
    }
    
    /**
     * 运行传统模式演示
     */
    private void runTraditionalModeDemo() {
        log.info("--- 传统模式文件传输演示 ---");
        
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(SERVER_HOST)
                .serverPort(SERVER_PORT)
                .auth(TEST_USER, TEST_SECRET)
                .chunkSize(512 * 1024) // 512KB分块
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();
        
        try (FileTransferClient client = new FileTransferClient(config)) {
            // 创建测试文件
            File testFile = createTestFile(TEST_FILE_NAME, SMALL_FILE_SIZE_KB * 1024);
            
            // 创建传输监听器
            DemoTransferListener listener = new DemoTransferListener();
            
            // 1. 文件上传测试
            log.info("1. 执行文件上传测试...");
            UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, listener);
            
            if (uploadResult.isSuccess()) {
                log.info("✅ 文件上传成功 - 文件ID: {}, 传输ID: {}", 
                        uploadResult.getFileId(), uploadResult.getTransferId());
                
                // 2. 文件信息查询测试
                log.info("2. 执行文件信息查询测试...");
                FileInfo fileInfo = client.getFileInfo(uploadResult.getFileId());
                log.info("✅ 文件信息查询成功 - 名称: {}, 大小: {}, 类型: {}", 
                        fileInfo.getFileName(), fileInfo.getFormattedSize(), fileInfo.getFileType());
                
                // 3. 文件下载测试
                log.info("3. 执行文件下载测试...");
                String downloadPath = DOWNLOAD_DIR + "/traditional-" + fileInfo.getFileName();
                CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                        fileInfo.getFileId(), downloadPath, new DemoTransferListener());
                
                DownloadResult downloadResult = downloadFuture.get(OPERATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                if (downloadResult.isSuccess()) {
                    log.info("✅ 文件下载成功: {}", downloadResult.getLocalPath());
                } else {
                    log.error("❌ 文件下载失败: {}", downloadResult.getErrorMessage());
                }
                
                // 4. 文件删除测试（传统模式暂时跳过，因为客户端SDK可能没有删除方法）
                log.info("4. 文件删除测试（传统模式暂时跳过）...");
                log.info("✅ 传统模式文件删除测试跳过");
                
            } else {
                log.error("❌ 文件上传失败: {}", uploadResult.getErrorMessage());
            }
            
            // 清理测试文件
            if (testFile.exists()) {
                testFile.delete();
            }
            
        } catch (Exception e) {
            log.error("传统模式演示过程中发生错误", e);
        }
    }
    
    /**
     * 运行S3风格演示
     */
    private void runS3StyleDemo() {
        log.info("--- S3风格文件传输演示 ---");
        
        // 创建S3客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(SERVER_HOST)
                .serverPort(SERVER_PORT)
                .auth(DEMO_USER, DEMO_SECRET)
                .chunkSize(1024 * 1024) // 1MB分块
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();
        
        try (S3FileTransferClient client = new S3FileTransferClient(config)) {
            // 创建测试文件
            File testFile = createTestFile("s3-" + TEST_FILE_NAME, SMALL_FILE_SIZE_KB * 1024);
            String objectKey = "demo-files/s3-test-file.txt";
            
            // 创建传输监听器
            DemoTransferListener listener = new DemoTransferListener();
            
            // 1. S3文件上传测试
            log.info("1. 执行S3文件上传测试...");
            S3PutObjectResult putResult = client.putObject(DEMO_BUCKET, objectKey, testFile, listener);
            
            if (putResult.isSuccess()) {
                log.info("✅ S3文件上传成功 - Key: {}, ETag: {}", putResult.getKey(), putResult.getEtag());
                
                // 2. S3对象信息查询测试
                log.info("2. 执行S3对象信息查询测试...");
                S3ObjectInfo objectInfo = client.getObjectInfo(DEMO_BUCKET, objectKey);
                if (objectInfo != null) {
                    log.info("✅ S3对象信息查询成功 - Key: {}, 大小: {}, ETag: {}", 
                            objectInfo.getKey(), objectInfo.getSize(), objectInfo.getEtag());
                }
                
                // 3. S3文件下载测试
                log.info("3. 执行S3文件下载测试...");
                String downloadPath = DOWNLOAD_DIR + "/s3-downloaded-file.txt";
                CompletableFuture<S3GetObjectResult> downloadFuture = client.getObjectAsync(
                        DEMO_BUCKET, objectKey, downloadPath, new DemoTransferListener());
                
                S3GetObjectResult downloadResult = downloadFuture.get(OPERATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                if (downloadResult.isSuccess()) {
                    log.info("✅ S3文件下载成功: {}", downloadResult.getLocalPath());
                } else {
                    log.error("❌ S3文件下载失败: {}", downloadResult.getErrorMessage());
                }
                
                // 4. S3对象列表测试
                log.info("4. 执行S3对象列表测试...");
                S3ListObjectsResult listResult = client.listObjects(DEMO_BUCKET, "demo-files/", null, 10);
                if (listResult.isSuccess() && listResult.getListObjectsResponse() != null) {
                    List<S3ObjectInfo> objects = listResult.getListObjectsResponse().getContents();
                    log.info("✅ S3对象列表查询成功，找到 {} 个对象", objects != null ? objects.size() : 0);
                    if (objects != null) {
                        objects.forEach(obj ->
                                log.info("   - {}: {} bytes", obj.getKey(), obj.getSize()));
                    }
                }
                
                // 5. S3文件删除测试
                log.info("5. 执行S3文件删除测试...");
                boolean deleteResult = client.deleteObject(DEMO_BUCKET, objectKey);
                if (deleteResult) {
                    log.info("✅ S3文件删除成功");
                } else {
                    log.error("❌ S3文件删除失败");
                }
                
            } else {
                log.error("❌ S3文件上传失败: {}", putResult.getErrorMessage());
            }
            
            // 清理测试文件
            if (testFile.exists()) {
                testFile.delete();
            }
            
        } catch (Exception e) {
            log.error("S3风格演示过程中发生错误", e);
        }
    }
    
    /**
     * 创建测试文件
     * 
     * @param fileName 文件名
     * @param sizeBytes 文件大小（字节）
     * @return 创建的文件对象
     */
    private File createTestFile(String fileName, int sizeBytes) {
        try {
            File file = new File(fileName);
            
            try (FileWriter writer = new FileWriter(file)) {
                // 写入文件头信息
                writer.write("=== 文件传输SDK测试文件 ===\n");
                writer.write("文件名: " + fileName + "\n");
                writer.write("创建时间: " + System.currentTimeMillis() + "\n");
                writer.write("目标大小: " + sizeBytes + " bytes\n");
                writer.write(createSeparator(50) + "\n");
                
                // 填充内容到指定大小
                String content = "这是测试内容，用于验证文件传输功能的正确性。";
                int headerSize = (int) file.length();
                int remainingSize = sizeBytes - headerSize;
                
                while (remainingSize > 0) {
                    String line = content + " [" + (sizeBytes - remainingSize) + "]\n";
                    if (line.length() <= remainingSize) {
                        writer.write(line);
                        remainingSize -= line.length();
                    } else {
                        writer.write(content.substring(0, remainingSize));
                        break;
                    }
                }
            }
            
            log.info("创建测试文件：{} (大小: {} bytes)", fileName, file.length());
            return file;
            
        } catch (IOException e) {
            throw new RuntimeException("创建测试文件失败: " + fileName, e);
        }
    }
    
    /**
     * 创建分隔符字符串（Java 8兼容）
     *
     * @param count 重复次数
     * @return 分隔符字符串
     */
    private static String createSeparator(int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append("=");
        }
        return sb.toString();
    }

    /**
     * 演示传输监听器
     */
    private static class DemoTransferListener implements TransferListener {
        private long lastLogTime = 0;
        private static final long LOG_INTERVAL_MS = 1000; // 每秒记录一次日志
        
        @Override
        public void onProgress(TransferProgress progress) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
                log.info("传输进度: {:.1f}% ({}/{} bytes, 速度: {}/s)",
                        progress.getProgress(),
                        progress.getTransferredSize(),
                        progress.getTotalSize(),
                        formatBytes(progress.getSpeed() != null ? progress.getSpeed() : 0L));
                lastLogTime = currentTime;
            }
        }
        
        @Override
        public void onCompleted(TransferProgress progress) {
            log.info("传输完成");
        }

        @Override
        public void onError(TransferProgress progress, Throwable error) {
            log.error("传输错误: {}", error.getMessage());
        }
        
        private String formatBytes(long bytes) {
            if (bytes < 1024) return bytes + "B";
            if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0);
            if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024));
            return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 运行性能测试
     */
    private void runPerformanceTests() {
        log.info("--- 性能测试演示 ---");

        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(SERVER_HOST)
                .serverPort(SERVER_PORT)
                .auth(TEST_USER, TEST_SECRET)
                .chunkSize(2 * 1024 * 1024) // 2MB分块，提高大文件传输性能
                .maxConcurrentTransfers(5)
                .retry(3, 1000)
                .build();

        try (FileTransferClient client = new FileTransferClient(config)) {
            // 1. 中等文件性能测试
            log.info("1. 执行中等文件性能测试 ({}MB)...", MEDIUM_FILE_SIZE_MB);
            performMediumFileTest(client);

            // 2. 大文件性能测试
            log.info("2. 执行大文件性能测试 ({}MB)...", LARGE_FILE_SIZE_MB);
            performLargeFileTest(client);

            // 3. 并发传输测试
            log.info("3. 执行并发传输测试...");
            performConcurrentTransferTest(client);

        } catch (Exception e) {
            log.error("性能测试过程中发生错误", e);
        }
    }

    /**
     * 中等文件性能测试
     */
    private void performMediumFileTest(FileTransferClient client) {
        try {
            File testFile = createTestFile("medium-" + TEST_FILE_NAME, MEDIUM_FILE_SIZE_MB * 1024 * 1024);
            DemoTransferListener listener = new DemoTransferListener();

            // 上传性能测试
            long uploadStartTime = System.currentTimeMillis();
            UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, listener);
            long uploadEndTime = System.currentTimeMillis();

            if (uploadResult.isSuccess()) {
                long uploadDuration = uploadEndTime - uploadStartTime;
                double uploadThroughputMBps = (MEDIUM_FILE_SIZE_MB * 1000.0) / uploadDuration;
                log.info("✅ 中等文件上传性能 - 大小: {}MB, 耗时: {}ms, 吞吐量: {:.2f} MB/s",
                        MEDIUM_FILE_SIZE_MB, uploadDuration, uploadThroughputMBps);

                // 下载性能测试
                String downloadPath = DOWNLOAD_DIR + "/medium-downloaded-" + testFile.getName();
                long downloadStartTime = System.currentTimeMillis();
                CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                        uploadResult.getFileId(), downloadPath, new DemoTransferListener());
                DownloadResult downloadResult = downloadFuture.get(OPERATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                long downloadEndTime = System.currentTimeMillis();

                if (downloadResult.isSuccess()) {
                    long downloadDuration = downloadEndTime - downloadStartTime;
                    double downloadThroughputMBps = (MEDIUM_FILE_SIZE_MB * 1000.0) / downloadDuration;
                    log.info("✅ 中等文件下载性能 - 大小: {}MB, 耗时: {}ms, 吞吐量: {:.2f} MB/s",
                            MEDIUM_FILE_SIZE_MB, downloadDuration, downloadThroughputMBps);
                }

                // 清理服务器文件（传统模式暂时跳过）
                log.info("清理服务器文件（传统模式暂时跳过）");
            }

            // 清理本地文件
            if (testFile.exists()) {
                testFile.delete();
            }

        } catch (Exception e) {
            log.error("中等文件性能测试失败", e);
        }
    }

    /**
     * 大文件性能测试
     */
    private void performLargeFileTest(FileTransferClient client) {
        try {
            File testFile = createTestFile(LARGE_TEST_FILE_NAME, LARGE_FILE_SIZE_MB * 1024 * 1024);
            DemoTransferListener listener = new DemoTransferListener();

            // 大文件上传性能测试
            long uploadStartTime = System.currentTimeMillis();
            UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, listener);
            long uploadEndTime = System.currentTimeMillis();

            if (uploadResult.isSuccess()) {
                long uploadDuration = uploadEndTime - uploadStartTime;
                double uploadThroughputMBps = (LARGE_FILE_SIZE_MB * 1000.0) / uploadDuration;
                log.info("✅ 大文件上传性能 - 大小: {}MB, 耗时: {}ms, 吞吐量: {:.2f} MB/s",
                        LARGE_FILE_SIZE_MB, uploadDuration, uploadThroughputMBps);

                // 验证性能指标
                if (uploadThroughputMBps > 1.0) {
                    log.info("✅ 上传性能达标 (> 1.0 MB/s)");
                } else {
                    log.warn("⚠️ 上传性能较低 ({:.2f} MB/s)", uploadThroughputMBps);
                }

                // 大文件下载性能测试
                String downloadPath = DOWNLOAD_DIR + "/large-downloaded-" + testFile.getName();
                long downloadStartTime = System.currentTimeMillis();
                CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                        uploadResult.getFileId(), downloadPath, new DemoTransferListener());
                DownloadResult downloadResult = downloadFuture.get(LARGE_FILE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                long downloadEndTime = System.currentTimeMillis();

                if (downloadResult.isSuccess()) {
                    long downloadDuration = downloadEndTime - downloadStartTime;
                    double downloadThroughputMBps = (LARGE_FILE_SIZE_MB * 1000.0) / downloadDuration;
                    log.info("✅ 大文件下载性能 - 大小: {}MB, 耗时: {}ms, 吞吐量: {:.2f} MB/s",
                            LARGE_FILE_SIZE_MB, downloadDuration, downloadThroughputMBps);

                    // 验证性能指标
                    if (downloadThroughputMBps > 1.0) {
                        log.info("✅ 下载性能达标 (> 1.0 MB/s)");
                    } else {
                        log.warn("⚠️ 下载性能较低 ({:.2f} MB/s)", downloadThroughputMBps);
                    }
                }

                // 清理服务器文件（传统模式暂时跳过）
                log.info("清理服务器文件（传统模式暂时跳过）");
            }

            // 清理本地文件
            if (testFile.exists()) {
                testFile.delete();
            }

        } catch (Exception e) {
            log.error("大文件性能测试失败", e);
        }
    }

    /**
     * 并发传输测试
     */
    private void performConcurrentTransferTest(FileTransferClient client) {
        try {
            int concurrentCount = 3;
            CompletableFuture<UploadResult>[] uploadFutures = new CompletableFuture[concurrentCount];
            File[] testFiles = new File[concurrentCount];

            // 创建多个测试文件并启动并发上传
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < concurrentCount; i++) {
                testFiles[i] = createTestFile("concurrent-" + i + "-" + TEST_FILE_NAME, SMALL_FILE_SIZE_KB * 1024);
                uploadFutures[i] = client.uploadFile(testFiles[i].getAbsolutePath(), null, new DemoTransferListener());
            }

            // 等待所有上传完成
            UploadResult[] uploadResults = new UploadResult[concurrentCount];
            for (int i = 0; i < concurrentCount; i++) {
                uploadResults[i] = uploadFutures[i].get(OPERATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            }
            long endTime = System.currentTimeMillis();

            long totalDuration = endTime - startTime;
            int successCount = 0;
            for (UploadResult result : uploadResults) {
                if (result.isSuccess()) {
                    successCount++;
                }
            }

            log.info("✅ 并发上传测试完成 - 成功: {}/{}, 总耗时: {}ms",
                    successCount, concurrentCount, totalDuration);

            // 清理文件
            for (int i = 0; i < concurrentCount; i++) {
                if (uploadResults[i].isSuccess()) {
                    log.info("清理服务器文件（传统模式暂时跳过）: {}", uploadResults[i].getFileId());
                }
                if (testFiles[i].exists()) {
                    testFiles[i].delete();
                }
            }

        } catch (Exception e) {
            log.error("并发传输测试失败", e);
        }
    }

    /**
     * 运行错误处理测试
     */
    private void runErrorHandlingTests() {
        log.info("--- 错误处理测试演示 ---");

        // 1. 无效服务器地址测试
        log.info("1. 执行无效服务器地址测试...");
        testInvalidServerAddress();

        // 2. 无效认证信息测试
        log.info("2. 执行无效认证信息测试...");
        testInvalidAuthentication();

        // 3. 不存在文件下载测试
        log.info("3. 执行不存在文件下载测试...");
        testNonExistentFileDownload();

        // 4. 网络超时测试
        log.info("4. 执行网络超时测试...");
        testNetworkTimeout();
    }

    /**
     * 测试无效服务器地址
     */
    private void testInvalidServerAddress() {
        try {
            ClientConfig config = ClientConfigBuilder.create()
                    .serverAddr("invalid-server-address")
                    .serverPort(99999)
                    .auth(TEST_USER, TEST_SECRET)
                    .build();

            try (FileTransferClient client = new FileTransferClient(config)) {
                File testFile = createTestFile("error-test-file.txt", 1024);
                UploadResult result = client.uploadFileSync(testFile.getAbsolutePath(), null, new DemoTransferListener());

                if (!result.isSuccess()) {
                    log.info("✅ 无效服务器地址错误处理正确: {}", result.getErrorMessage());
                } else {
                    log.warn("⚠️ 无效服务器地址测试异常：应该失败但成功了");
                }

                if (testFile.exists()) {
                    testFile.delete();
                }
            }
        } catch (Exception e) {
            log.info("✅ 无效服务器地址错误处理正确: {}", e.getMessage());
        }
    }

    /**
     * 测试无效认证信息
     */
    private void testInvalidAuthentication() {
        try {
            ClientConfig config = ClientConfigBuilder.create()
                    .serverAddr(SERVER_HOST)
                    .serverPort(SERVER_PORT)
                    .auth("invalid-user", "invalid-secret")
                    .build();

            try (FileTransferClient client = new FileTransferClient(config)) {
                File testFile = createTestFile("auth-error-test-file.txt", 1024);
                UploadResult result = client.uploadFileSync(testFile.getAbsolutePath(), null, new DemoTransferListener());

                if (!result.isSuccess()) {
                    log.info("✅ 无效认证信息错误处理正确: {}", result.getErrorMessage());
                } else {
                    log.warn("⚠️ 无效认证信息测试异常：应该失败但成功了");
                }

                if (testFile.exists()) {
                    testFile.delete();
                }
            }
        } catch (Exception e) {
            log.info("✅ 无效认证信息错误处理正确: {}", e.getMessage());
        }
    }

    /**
     * 测试不存在文件下载
     */
    private void testNonExistentFileDownload() {
        try {
            ClientConfig config = ClientConfigBuilder.create()
                    .serverAddr(SERVER_HOST)
                    .serverPort(SERVER_PORT)
                    .auth(TEST_USER, TEST_SECRET)
                    .build();

            try (FileTransferClient client = new FileTransferClient(config)) {
                String nonExistentFileId = "non-existent-file-id-12345";
                String downloadPath = DOWNLOAD_DIR + "/non-existent-file.txt";

                CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                        nonExistentFileId, downloadPath, new DemoTransferListener());
                DownloadResult result = downloadFuture.get(OPERATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);

                if (!result.isSuccess()) {
                    log.info("✅ 不存在文件下载错误处理正确: {}", result.getErrorMessage());
                } else {
                    log.warn("⚠️ 不存在文件下载测试异常：应该失败但成功了");
                }
            }
        } catch (Exception e) {
            log.info("✅ 不存在文件下载错误处理正确: {}", e.getMessage());
        }
    }

    /**
     * 测试网络超时
     */
    private void testNetworkTimeout() {
        try {
            ClientConfig config = ClientConfigBuilder.create()
                    .serverAddr(SERVER_HOST)
                    .serverPort(SERVER_PORT)
                    .auth(TEST_USER, TEST_SECRET)
                    .build();

            try (FileTransferClient client = new FileTransferClient(config)) {
                File testFile = createTestFile("timeout-test-file.txt", 1024);
                UploadResult result = client.uploadFileSync(testFile.getAbsolutePath(), null, new DemoTransferListener());

                if (!result.isSuccess()) {
                    log.info("✅ 网络超时错误处理正确: {}", result.getErrorMessage());
                } else {
                    log.warn("⚠️ 网络超时测试异常：应该失败但成功了");
                }

                if (testFile.exists()) {
                    testFile.delete();
                }
            }
        } catch (Exception e) {
            log.info("✅ 网络超时错误处理正确: {}", e.getMessage());
        }
    }
}
