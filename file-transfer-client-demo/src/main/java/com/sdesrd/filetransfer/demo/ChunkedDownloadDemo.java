package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import com.sdesrd.filetransfer.client.S3FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.ChunkedDownloadRequest;
import com.sdesrd.filetransfer.client.dto.ChunkedDownloadResult;
import com.sdesrd.filetransfer.client.dto.S3PutObjectResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.retry.RetryPolicy;

import lombok.extern.slf4j.Slf4j;

/**
 * 分片下载演示程序
 * 展示如何使用分片下载功能，包括断点续传和重传机制
 */
@Slf4j
public class ChunkedDownloadDemo {
    
    /** 演示用的存储桶名称 */
    private static final String DEMO_BUCKET = "default";
    
    /** 演示用的对象key */
    private static final String DEMO_KEY = "demo/chunked-download-test.txt";
    
    /** 演示用的本地文件路径 */
    private static final String LOCAL_UPLOAD_PATH = "./demo-upload-file.txt";
    
    /** 演示用的本地下载路径 */
    private static final String LOCAL_DOWNLOAD_PATH = "./demo-download-file.txt";
    
    /** 分块大小：1MB */
    private static final int CHUNK_SIZE = 1024 * 1024;
    
    public static void main(String[] args) {
        ChunkedDownloadDemo demo = new ChunkedDownloadDemo();
        
        try {
            log.info("=== 分片下载演示开始 ===");
            
            // 1. 创建客户端配置
            ClientConfig config = demo.createClientConfig();
            
            // 2. 创建客户端
            try (S3FileTransferClient client = new S3FileTransferClient(config)) {
                
                // 3. 准备测试文件
                demo.prepareTestFile();
                
                // 4. 上传测试文件
                demo.uploadTestFile(client);
                
                // 5. 演示分片下载
                demo.demonstrateChunkedDownload(client);
                
                // 6. 演示断点续传
                demo.demonstrateResumeDownload(client);
                
                // 7. 演示重传机制
                demo.demonstrateRetryMechanism(client);
                
            }
            
            log.info("=== 分片下载演示完成 ===");
            
        } catch (Exception e) {
            log.error("演示程序执行失败", e);
            System.exit(1);
        }
    }
    
    /**
     * 创建客户端配置
     */
    private ClientConfig createClientConfig() {
        log.info("创建客户端配置...");
        
        // 创建自定义重传策略
        RetryPolicy retryPolicy = RetryPolicy.defaultPolicy();
        retryPolicy.setMaxRetryCount(5);
        retryPolicy.setBaseDelayMs(1000L);
        retryPolicy.setBackoffStrategy(RetryPolicy.BackoffStrategy.EXPONENTIAL);
        retryPolicy.setJitterEnabled(true);
        
        return ClientConfigBuilder.create()
                .serverAddr("localhost")
                .serverPort(49011)
                .auth("user1", "user1-secret-2024")
                .chunkSize(CHUNK_SIZE)
                .retryPolicy(retryPolicy)
                .enableConcurrentChunkTransfer(3)
                .build();
    }
    
    /**
     * 准备测试文件
     */
    private void prepareTestFile() throws IOException {
        log.info("准备测试文件...");
        
        // 创建一个较大的测试文件（5MB）
        StringBuilder content = new StringBuilder();
        String line = "这是分片下载测试文件的内容行。包含中文字符以测试编码处理。Line number: ";
        
        for (int i = 0; i < 50000; i++) {
            content.append(line).append(i).append("\n");
        }
        
        Files.write(Paths.get(LOCAL_UPLOAD_PATH), content.toString().getBytes("UTF-8"));
        
        File testFile = new File(LOCAL_UPLOAD_PATH);
        log.info("测试文件已创建 - 路径: {}, 大小: {} bytes", LOCAL_UPLOAD_PATH, testFile.length());
    }
    
    /**
     * 上传测试文件
     */
    private void uploadTestFile(S3FileTransferClient client) throws FileTransferException {
        log.info("上传测试文件到服务器...");
        
        TransferListener uploadListener = new TransferListener() {
            @Override
            public void onStart(TransferProgress progress) {
                log.info("开始上传 - 文件: {}, 大小: {} bytes", 
                        progress.getFileName(), progress.getTotalSize());
            }
            
            @Override
            public void onProgress(TransferProgress progress) {
                if (progress.getCompletedChunks() % 5 == 0) { // 每5个分块打印一次进度
                    log.info("上传进度 - {}% ({}/{})", 
                            String.format("%.1f", progress.getProgress()),
                            progress.getCompletedChunks(), progress.getTotalChunks());
                }
            }
            
            @Override
            public void onCompleted(TransferProgress progress) {
                log.info("上传完成 - 文件: {}", progress.getFileName());
            }
            
            @Override
            public void onError(TransferProgress progress, Throwable error) {
                log.error("上传失败 - 文件: {}, 错误: {}", progress.getFileName(), error.getMessage());
            }
        };
        
        S3PutObjectResult uploadResult = client.putObject(DEMO_BUCKET, DEMO_KEY, LOCAL_UPLOAD_PATH, uploadListener);
        log.info("文件上传成功 - ETag: {}", uploadResult.getEtag());
    }
    
    /**
     * 演示分片下载
     */
    private void demonstrateChunkedDownload(S3FileTransferClient client) throws FileTransferException {
        log.info("=== 演示分片下载 ===");
        
        // 删除之前的下载文件
        File downloadFile = new File(LOCAL_DOWNLOAD_PATH);
        if (downloadFile.exists()) {
            downloadFile.delete();
        }
        
        // 创建下载请求
        ChunkedDownloadRequest request = new ChunkedDownloadRequest();
        request.setBucket(DEMO_BUCKET);
        request.setKey(DEMO_KEY);
        request.setLocalPath(LOCAL_DOWNLOAD_PATH);
        request.setChunkSize(CHUNK_SIZE);
        request.setOverwrite(true);
        request.setResumeEnabled(true);
        request.setMaxConcurrentChunks(3);
        
        // 创建下载监听器
        TransferListener downloadListener = new TransferListener() {
            @Override
            public void onStart(TransferProgress progress) {
                log.info("开始分片下载 - 文件: {}, 大小: {} bytes, 分块数: {}", 
                        progress.getFileName(), progress.getTotalSize(), progress.getTotalChunks());
            }
            
            @Override
            public void onProgress(TransferProgress progress) {
                log.info("下载进度 - {}% ({}/{})", 
                        String.format("%.1f", progress.getProgress()),
                        progress.getCompletedChunks(), progress.getTotalChunks());
            }
            
            @Override
            public void onCompleted(TransferProgress progress) {
                log.info("分片下载完成 - 文件: {}", progress.getFileName());
            }
            
            @Override
            public void onError(TransferProgress progress, Throwable error) {
                log.error("分片下载失败 - 文件: {}, 错误: {}", progress.getFileName(), error.getMessage());
            }
        };
        
        // 执行分片下载
        ChunkedDownloadResult result = client.chunkedDownload(request, downloadListener);
        
        // 输出结果
        log.info("分片下载结果:");
        log.info("  下载ID: {}", result.getDownloadId());
        log.info("  状态: {}", result.getStatus());
        log.info("  成功: {}", result.isSuccess());
        log.info("  文件大小: {} bytes", result.getFileSize());
        log.info("  总分块数: {}", result.getTotalChunks());
        log.info("  已下载分块数: {}", result.getDownloadedChunks());
        log.info("  进度: {}%", String.format("%.1f", result.getProgress()));
        log.info("  耗时: {} ms", result.getDuration());
        
        if (result.getAverageSpeed() != null) {
            log.info("  平均速度: {} KB/s", String.format("%.2f", result.getAverageSpeed() / 1024));
        }
        
        // 验证下载的文件
        verifyDownloadedFile();
    }
    
    /**
     * 演示断点续传（模拟）
     */
    private void demonstrateResumeDownload(S3FileTransferClient client) {
        log.info("=== 演示断点续传功能 ===");
        log.info("注意：这是一个模拟演示，实际的断点续传需要在网络中断后重新启动下载");
        
        // 在实际应用中，断点续传的流程是：
        // 1. 检查本地是否有未完成的下载任务
        // 2. 如果有，获取已下载的分块信息
        // 3. 从未下载的分块开始继续下载
        // 4. 合并所有分块到最终文件
        
        log.info("断点续传功能已集成到分片下载中，当网络中断时会自动重试失败的分块");
    }
    
    /**
     * 演示重传机制
     */
    private void demonstrateRetryMechanism(S3FileTransferClient client) {
        log.info("=== 演示重传机制 ===");
        log.info("重传机制已集成到客户端配置中，支持以下特性：");
        log.info("  - 可配置的重试次数和延迟策略");
        log.info("  - 指数退避算法，避免服务器压力");
        log.info("  - 抖动机制，防止惊群效应");
        log.info("  - 智能异常识别，只重试可恢复的错误");
        log.info("  - 重试回调，支持自定义重试逻辑");
    }
    
    /**
     * 验证下载的文件
     */
    private void verifyDownloadedFile() {
        try {
            File originalFile = new File(LOCAL_UPLOAD_PATH);
            File downloadedFile = new File(LOCAL_DOWNLOAD_PATH);
            
            if (!downloadedFile.exists()) {
                log.error("下载的文件不存在: {}", LOCAL_DOWNLOAD_PATH);
                return;
            }
            
            long originalSize = originalFile.length();
            long downloadedSize = downloadedFile.length();
            
            log.info("文件大小验证:");
            log.info("  原始文件: {} bytes", originalSize);
            log.info("  下载文件: {} bytes", downloadedSize);
            
            boolean sizeValid = (originalSize == downloadedSize);
            if (sizeValid) {
                log.info("✓ 文件大小验证通过");
            } else {
                log.error("✗ 文件大小验证失败");
            }
            
            // MD5值验证
            log.info("开始计算文件MD5值...");
            long startTime = System.currentTimeMillis();
            
            String originalMd5 = com.sdesrd.filetransfer.client.util.FileUtils.calculateMD5(originalFile);
            String downloadedMd5 = com.sdesrd.filetransfer.client.util.FileUtils.calculateMD5(downloadedFile);
            
            long endTime = System.currentTimeMillis();
            
            log.info("MD5值验证:");
            log.info("  原始文件MD5: {}", originalMd5);
            log.info("  下载文件MD5: {}", downloadedMd5);
            log.info("  计算耗时: {} ms", endTime - startTime);
            
            boolean md5Valid = originalMd5.equals(downloadedMd5);
            if (md5Valid) {
                log.info("✓ MD5值验证通过");
            } else {
                log.error("✗ MD5值验证失败");
            }
            
            // 综合验证结果
            if (sizeValid && md5Valid) {
                log.info("🎉 文件完整性验证全部通过，下载文件与原始文件完全一致！");
            } else {
                log.error("❌ 文件完整性验证失败，下载文件可能存在问题！");
            }
            
        } catch (Exception e) {
            log.error("验证下载文件时发生错误", e);
        }
    }
}
