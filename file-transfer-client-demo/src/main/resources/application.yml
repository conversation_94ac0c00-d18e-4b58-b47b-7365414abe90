# 文件传输客户端演示应用配置
spring:
  application:
    name: file-transfer-client-demo

# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: INFO
    org.springframework: WARN
    org.apache.http: WARN
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# 客户端演示配置
demo:
  client:
    # 服务器配置
    server:
      host: localhost
      port: 49011
    
    # 测试用户配置
    users:
      test-user:
        username: "test-user"
        secret: "test-secret-key-2024"
        buckets: ["default"]
      demo:
        username: "demo"
        secret: "demo-secret-key-2024"
        buckets: ["default", "demo"]
    
    # 测试文件配置
    files:
      small-size-kb: 10      # 小文件大小（KB）
      medium-size-mb: 5      # 中等文件大小（MB）
      large-size-mb: 20      # 大文件大小（MB）
      download-dir: "./download"
    
    # 性能测试配置
    performance:
      min-throughput-mbps: 1.0    # 最小吞吐量要求（MB/s）
      concurrent-count: 3         # 并发传输数量
      timeout-seconds: 30         # 操作超时时间（秒）
      large-file-timeout-seconds: 120  # 大文件操作超时时间（秒）
