#!/bin/bash

# ================================================================================
# 文件传输独立服务端停止脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输独立服务端停止脚本"

# 文件路径
readonly PID_FILE="./server.pid"
readonly DEFAULT_SERVER_PORT=49011

# 超时配置
readonly SHUTDOWN_TIMEOUT=10 # 服务器关闭超时时间（秒）

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
}

log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
}

# ==================== 工具函数 ====================

# 检查服务器是否正在运行
is_server_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 服务器正在运行
        else
            # PID文件存在但进程不存在，清理PID文件
            rm -f "$PID_FILE"
        fi
    fi
    return 1  # 服务器未运行
}

# 获取服务器PID
get_server_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 停止服务器
stop_server() {
    log_info "停止文件传输服务器..."
    
    if ! is_server_running; then
        log_warning "服务器未运行"
        return 0
    fi
    
    local pid=$(get_server_pid)
    log_info "停止服务器进程（PID：$pid）..."
    
    # 发送TERM信号
    if kill "$pid" 2>/dev/null; then
        # 等待进程正常退出
        local wait_count=0
        while [ $wait_count -lt $SHUTDOWN_TIMEOUT ]; do
            if ! kill -0 "$pid" 2>/dev/null; then
                log_success "服务器已正常停止"
                rm -f "$PID_FILE"
                return 0
            fi
            sleep 1
            wait_count=$((wait_count + 1))
        done
        
        # 强制杀死进程
        log_warning "强制停止服务器进程..."
        if kill -9 "$pid" 2>/dev/null; then
            log_success "服务器已强制停止"
        else
            log_error "无法停止服务器进程"
            return 1
        fi
    else
        log_error "无法发送停止信号到进程：$pid"
        return 1
    fi
    
    rm -f "$PID_FILE"
    return 0
}

# 强制停止所有相关进程
force_stop_all() {
    log_info "强制停止所有文件传输服务器进程..."
    
    # 通过端口查找进程
    local port_pids=$(lsof -ti:$DEFAULT_SERVER_PORT 2>/dev/null || true)
    if [ -n "$port_pids" ]; then
        log_info "发现占用端口 $DEFAULT_SERVER_PORT 的进程：$port_pids"
        kill -9 $port_pids 2>/dev/null || true
        log_info "已强制停止端口相关进程"
    fi
    
    # 通过进程名查找
    local java_pids=$(pgrep -f "file-transfer-server-standalone" 2>/dev/null || true)
    if [ -n "$java_pids" ]; then
        log_info "发现文件传输服务器进程：$java_pids"
        kill -9 $java_pids 2>/dev/null || true
        log_info "已强制停止服务器进程"
    fi
    
    # 清理PID文件
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
        log_info "已清理PID文件"
    fi
    
    log_success "强制停止完成"
}

# 显示帮助信息
show_help() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "========================================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项："
    echo "  --force           强制停止所有相关进程"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "使用示例："
    echo "  $0                # 正常停止服务器"
    echo "  $0 --force        # 强制停止所有相关进程"
    echo ""
}

# ==================== 主程序 ====================

# 主函数
main() {
    local force_stop=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_stop=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
    
    # 执行停止操作
    if [ "$force_stop" = true ]; then
        force_stop_all
    else
        stop_server
    fi
}

# 执行主函数
main "$@"
