#!/bin/bash

# ================================================================================
# 文件传输独立服务端启动脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输独立服务端启动脚本"

# 默认配置
readonly DEFAULT_SERVER_PORT=49011
readonly DEFAULT_JAVA_HOME="$HOME/.jdks/corretto-1.8.0_452"
readonly DEFAULT_CONFIG_FILE="file-transfer-server.yml"

# 文件路径
readonly JAR_FILE="target/file-transfer-server-standalone-1.0.0.jar"
readonly PID_FILE="./server.pid"
readonly LOG_FILE="./logs/server.log"

# 超时配置
readonly STARTUP_TIMEOUT=30  # 服务器启动超时时间（秒）
readonly SHUTDOWN_TIMEOUT=10 # 服务器关闭超时时间（秒）

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
}

log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
}

# ==================== 工具函数 ====================

# 显示脚本头部信息
show_header() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
}

# 检查命令是否存在
check_command() {
    local command="$1"
    local description="$2"
    
    if ! command -v "$command" &> /dev/null; then
        log_error "$description 未安装或未在PATH中：$command"
        return 1
    fi
    return 0
}

# 设置Java环境
setup_java_environment() {
    local custom_java_home="$1"
    
    log_info "设置Java环境..."
    
    # 如果指定了自定义Java路径，使用它
    if [ -n "$custom_java_home" ]; then
        if [ -d "$custom_java_home" ] && [ -x "$custom_java_home/bin/java" ]; then
            export JAVA_HOME="$custom_java_home"
            export PATH="$custom_java_home/bin:$PATH"
            log_info "使用指定的Java JDK：$custom_java_home"
        else
            log_error "指定的Java JDK路径无效：$custom_java_home"
            return 1
        fi
    # 检查默认的Java 8 JDK是否存在
    elif [ -d "$DEFAULT_JAVA_HOME" ] && [ -x "$DEFAULT_JAVA_HOME/bin/java" ]; then
        export JAVA_HOME="$DEFAULT_JAVA_HOME"
        export PATH="$DEFAULT_JAVA_HOME/bin:$PATH"
        log_info "使用默认的Java 8 JDK：$DEFAULT_JAVA_HOME"
    # 使用系统默认Java
    else
        log_warning "未找到默认Java 8 JDK，使用系统默认Java"
    fi
    
    # 验证Java命令可用性
    if ! check_command "java" "Java运行时"; then
        return 1
    fi
    
    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"
    
    return 0
}

# 检查JAR文件是否存在
check_jar_file() {
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件不存在：$JAR_FILE"
        log_info "请先执行 'mvn clean package' 编译项目"
        return 1
    fi
    
    log_info "找到JAR文件：$JAR_FILE"
    return 0
}

# 创建必要的目录
create_directories() {
    local log_dir=$(dirname "$LOG_FILE")
    if [ ! -d "$log_dir" ]; then
        mkdir -p "$log_dir"
        log_info "创建日志目录：$log_dir"
    fi
    
    # 创建数据目录
    if [ ! -d "./data" ]; then
        mkdir -p "./data"
        log_info "创建数据目录：./data"
    fi
    
    return 0
}

# 检查服务器是否正在运行
is_server_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 服务器正在运行
        else
            # PID文件存在但进程不存在，清理PID文件
            rm -f "$PID_FILE"
        fi
    fi
    return 1  # 服务器未运行
}

# 获取服务器PID
get_server_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 等待服务器启动
wait_for_startup() {
    local port="$1"
    local timeout="$2"
    
    log_info "等待服务器启动（端口：$port，超时：${timeout}秒）..."
    
    local wait_count=0
    while [ $wait_count -lt $timeout ]; do
        # 检查端口是否可访问
        if curl -s "http://localhost:$port/actuator/health" >/dev/null 2>&1; then
            log_success "服务器启动成功"
            return 0
        fi
        
        # 检查进程是否还在运行
        if ! is_server_running; then
            log_error "服务器进程意外退出"
            return 1
        fi
        
        sleep 1
        wait_count=$((wait_count + 1))
        
        # 每5秒显示一次等待信息
        if [ $((wait_count % 5)) -eq 0 ]; then
            log_info "等待中... (${wait_count}/${timeout})"
        fi
    done
    
    log_error "服务器启动超时"
    return 1
}

# 启动服务器
start_server() {
    local port="$1"
    local config_file="$2"
    local background="$3"
    
    log_info "启动文件传输服务器..."
    
    # 检查服务器是否已经在运行
    if is_server_running; then
        local pid=$(get_server_pid)
        log_warning "服务器已经在运行（PID：$pid）"
        return 0
    fi
    
    # 构建Java命令
    local java_opts="-Xmx1g -Xms512m -XX:MaxPermSize=256m"
    local spring_opts="--server.port=$port"
    
    if [ -n "$config_file" ] && [ -f "$config_file" ]; then
        spring_opts="$spring_opts --spring.config.location=file:./$config_file"
        log_info "使用配置文件：$config_file"
    fi
    
    log_info "Java选项：$java_opts"
    log_info "Spring选项：$spring_opts"
    
    # 启动服务器
    if [ "$background" = true ]; then
        # 后台启动
        nohup java $java_opts -jar "$JAR_FILE" $spring_opts > "$LOG_FILE" 2>&1 &
        local server_pid=$!
        echo $server_pid > "$PID_FILE"
        
        log_info "服务器已在后台启动（PID：$server_pid）"
        log_info "日志文件：$LOG_FILE"
        
        # 等待服务器启动
        if wait_for_startup "$port" "$STARTUP_TIMEOUT"; then
            log_success "服务器启动完成"
            show_server_info "$port"
            return 0
        else
            log_error "服务器启动失败"
            stop_server
            return 1
        fi
    else
        # 前台启动
        log_info "在前台启动服务器（按Ctrl+C停止）..."
        java $java_opts -jar "$JAR_FILE" $spring_opts
    fi
}

# 停止服务器
stop_server() {
    log_info "停止文件传输服务器..."
    
    if ! is_server_running; then
        log_warning "服务器未运行"
        return 0
    fi
    
    local pid=$(get_server_pid)
    log_info "停止服务器进程（PID：$pid）..."
    
    # 发送TERM信号
    if kill "$pid" 2>/dev/null; then
        # 等待进程正常退出
        local wait_count=0
        while [ $wait_count -lt $SHUTDOWN_TIMEOUT ]; do
            if ! kill -0 "$pid" 2>/dev/null; then
                log_success "服务器已正常停止"
                rm -f "$PID_FILE"
                return 0
            fi
            sleep 1
            wait_count=$((wait_count + 1))
        done
        
        # 强制杀死进程
        log_warning "强制停止服务器进程..."
        if kill -9 "$pid" 2>/dev/null; then
            log_success "服务器已强制停止"
        else
            log_error "无法停止服务器进程"
            return 1
        fi
    else
        log_error "无法发送停止信号到进程：$pid"
        return 1
    fi
    
    rm -f "$PID_FILE"
    return 0
}

# 重启服务器
restart_server() {
    local port="$1"
    local config_file="$2"
    
    log_info "重启文件传输服务器..."
    
    # 停止服务器
    if is_server_running; then
        if ! stop_server; then
            log_error "无法停止服务器，重启失败"
            return 1
        fi
    fi
    
    # 等待一秒确保端口释放
    sleep 1
    
    # 启动服务器
    start_server "$port" "$config_file" true
}

# 显示服务器状态
show_status() {
    if is_server_running; then
        local pid=$(get_server_pid)
        log_success "服务器正在运行（PID：$pid）"
        
        # 尝试获取服务器信息
        local port=$(ps -p "$pid" -o args= | grep -o 'server.port=[0-9]*' | cut -d'=' -f2)
        if [ -z "$port" ]; then
            port="$DEFAULT_SERVER_PORT"
        fi
        
        show_server_info "$port"
        return 0
    else
        log_info "服务器未运行"
        return 1
    fi
}

# 显示服务器信息
show_server_info() {
    local port="$1"
    
    echo ""
    echo "=========================================="
    echo "           服务器信息"
    echo "=========================================="
    echo "🌐 API文档地址:"
    echo "   http://localhost:$port/doc.html"
    echo ""
    echo "🔍 健康检查:"
    echo "   http://localhost:$port/actuator/health"
    echo ""
    echo "📡 API端点:"
    echo "   传统模式: http://localhost:$port/filetransfer/api/file/*"
    echo "   S3风格:   http://localhost:$port/filetransfer/api/s3/*"
    echo "   管理接口: http://localhost:$port/filetransfer/api/admin/*"
    echo ""
    echo "📄 日志文件: $LOG_FILE"
    echo "📋 PID文件:  $PID_FILE"
    echo "=========================================="
}

# 显示帮助信息
show_help() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "========================================================"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令："
    echo "  start             启动服务器（后台运行）"
    echo "  stop              停止服务器"
    echo "  restart           重启服务器"
    echo "  status            显示服务器状态"
    echo "  run               前台运行服务器"
    echo "  help              显示此帮助信息"
    echo ""
    echo "选项："
    echo "  --port PORT       指定服务器端口（默认：$DEFAULT_SERVER_PORT）"
    echo "  --config FILE     指定配置文件（默认：$DEFAULT_CONFIG_FILE）"
    echo "  --java-home PATH  指定Java JDK路径"
    echo ""
    echo "使用示例："
    echo "  $0 start                              # 启动服务器"
    echo "  $0 start --port 8080                  # 在端口8080启动服务器"
    echo "  $0 start --config custom.yml          # 使用自定义配置文件启动"
    echo "  $0 stop                               # 停止服务器"
    echo "  $0 restart                            # 重启服务器"
    echo "  $0 status                             # 查看服务器状态"
    echo "  $0 run                                # 前台运行服务器"
    echo ""
    echo "默认配置："
    echo "  服务器端口: $DEFAULT_SERVER_PORT"
    echo "  Java路径:   $DEFAULT_JAVA_HOME"
    echo "  配置文件:   $DEFAULT_CONFIG_FILE"
    echo "  JAR文件:    $JAR_FILE"
    echo ""
}

# ==================== 主程序 ====================

# 主函数
main() {
    # 解析命令行参数
    local command=""
    local port="$DEFAULT_SERVER_PORT"
    local config_file="$DEFAULT_CONFIG_FILE"
    local custom_java_home=""
    
    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # 解析第一个参数作为命令
    command="$1"
    shift
    
    # 解析其余参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --port)
                port="$2"
                shift 2
                ;;
            --config)
                config_file="$2"
                shift 2
                ;;
            --java-home)
                custom_java_home="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示脚本头部信息
    show_header
    
    # 设置Java环境
    if ! setup_java_environment "$custom_java_home"; then
        exit 1
    fi
    
    # 创建必要的目录
    create_directories
    
    # 执行命令
    case $command in
        start)
            if ! check_jar_file; then
                exit 1
            fi
            start_server "$port" "$config_file" true
            ;;
        stop)
            stop_server
            ;;
        restart)
            if ! check_jar_file; then
                exit 1
            fi
            restart_server "$port" "$config_file"
            ;;
        status)
            show_status
            ;;
        run)
            if ! check_jar_file; then
                exit 1
            fi
            start_server "$port" "$config_file" false
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
