# 文件传输独立服务端配置文件
server:
  port: 49011
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: file-transfer-server

  # 数据源配置 - SQLite
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: ********************************************
    username:
    password:

  # 连接池配置
  druid:
    initial-size: 1
    min-idle: 1
    max-active: 10
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    validation-query: SELECT 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    default-executor-type: reuse
    # SQLite兼容性配置
    jdbc-type-for-null: null
  # 配置TypeHandler包扫描
  type-handlers-package: com.sdesrd.filetransfer.server.config
  global-config:
    db-config:
      # 逻辑删除
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 文件传输服务端配置
file:
  transfer:
    server:
      enabled: true

      # 存储配置
      database-path: ./data/file-transfer/database.db

      # 功能配置
      fast-upload-enabled: true
      rate-limit-enabled: true

      # 速度限制 (字节/秒)
      upload-rate-limit: 10485760      # 10MB/s
      download-rate-limit: 10485760    # 10MB/s

      # 文件大小限制
      default-chunk-size: 2097152      # 2MB
      max-file-size: 104857600         # 100MB
      max-in-memory-size: 10485760     # 10MB

      # 清理配置
      cleanup-interval: 3600000        # 1小时
      record-expire-time: 86400000     # 24小时

      # 服务配置
      swagger-enabled: true
      cors-enabled: true
      allowed-origins:
        - "*"

      # 存储桶配置
      buckets:
        default:
          name: "default"
          storage-path: "./data/file-transfer/buckets/default"
          upload-rate-limit: 52428800   # 50MB/s
          download-rate-limit: 52428800
          max-file-size: 104857600       # 100MB
          default-chunk-size: 524288     # 512KB
          max-in-memory-size: 10485760   # 10MB
          fast-upload-enabled: true
          rate-limit-enabled: false

        demo:
          name: "demo"
          storage-path: "./data/file-transfer/buckets/demo"
          upload-rate-limit: 5242880     # 5MB/s
          download-rate-limit: 5242880
          max-file-size: 52428800        # 50MB
          default-chunk-size: 1048576    # 1MB
          max-in-memory-size: 10485760   # 10MB
          fast-upload-enabled: true
          rate-limit-enabled: false

      # 用户配置
      users:
        test-user:
          secret-key: "test-secret-key-2024"
          allowed-buckets: ["default"]
          upload-rate-limit: 52428800
          download-rate-limit: 52428800
          max-file-size: 104857600
          rate-limit-enabled: false

        demo:
          secret-key: "demo-secret-key-2024"
          allowed-buckets: ["default", "demo"]
          upload-rate-limit: 5242880
          download-rate-limit: 5242880
          max-file-size: 52428800
          rate-limit-enabled: false

# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/file-transfer-server.log
    max-size: 10MB
    max-history: 30

# 监控端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always 