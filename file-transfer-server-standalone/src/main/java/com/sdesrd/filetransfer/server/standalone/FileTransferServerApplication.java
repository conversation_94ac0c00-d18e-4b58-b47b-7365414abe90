package com.sdesrd.filetransfer.server.standalone;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * 文件传输服务端独立应用
 *
 * 这是一个独立的文件传输服务端应用，可以作为单独的进程运行
 * 通过引入file-transfer-server-sdk提供完整的文件传输服务功能
 *
 * 主要特性：
 * - 独立运行：可作为独立进程运行，无需集成到其他应用
 * - 完整功能：包含所有文件传输SDK的功能
 * - S3风格API：支持类似AWS S3的对象存储API
 * - 断点续传：支持文件上传下载中断后继续传输
 * - 传输限速：可配置上传下载速度限制
 * - 监控支持：内置健康检查和监控端点
 * - 配置灵活：支持外部配置文件和环境变量
 */
@SpringBootApplication(scanBasePackages = {"com.sdesrd.filetransfer"})
@ConfigurationPropertiesScan(basePackages = {"com.sdesrd.filetransfer.server.config"})
public class FileTransferServerApplication {

    public static void main(String[] args) {
        // 设置默认配置文件
        System.setProperty("spring.config.name", "file-transfer-server");

        SpringApplication app = new SpringApplication(FileTransferServerApplication.class);

        // 设置默认profile
        app.setAdditionalProfiles("server");

        // 启动应用
        ConfigurableApplicationContext context = app.run(args);

        // 获取环境信息
        Environment env = context.getEnvironment();
        String port = env.getProperty("server.port", "49011");
        String contextPath = env.getProperty("server.servlet.context-path", "");

        // 显示启动信息
        showStartupInfo(port, contextPath);
    }

    /**
     * 显示服务启动信息
     *
     * @param port 服务端口
     * @param contextPath 上下文路径
     */
    private static void showStartupInfo(String port, String contextPath) {
        System.out.println("===========================================");
        System.out.println("文件传输独立服务端启动成功！");
        System.out.println("");
        System.out.println("🌐 API文档地址:");
        System.out.println("   http://localhost:" + port + contextPath + "/doc.html");
        System.out.println("");
        System.out.println("🔍 健康检查:");
        System.out.println("   http://localhost:" + port + contextPath + "/actuator/health");
        System.out.println("");
        System.out.println("📡 API端点:");
        System.out.println("   传统模式: http://localhost:" + port + contextPath + "/filetransfer/api/file/*");
        System.out.println("   S3风格:   http://localhost:" + port + contextPath + "/filetransfer/api/s3/*");
        System.out.println("   管理接口: http://localhost:" + port + contextPath + "/filetransfer/api/admin/*");
        System.out.println("");
        System.out.println("💡 主要特性:");
        System.out.println("   ✅ 支持断点续传和分片上传下载");
        System.out.println("   ✅ 基于用户的差异化限速控制");
        System.out.println("   ✅ S3风格的key-value对象存储");
        System.out.println("   ✅ 文件MD5校验和秒传功能");
        System.out.println("   ✅ 实时传输进度监控");
        System.out.println("");
        System.out.println("🔑 S3风格API示例:");
        System.out.println("   PUT    /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   GET    /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   DELETE /filetransfer/api/s3/documents/file.pdf");
        System.out.println("   GET    /filetransfer/api/s3/?prefix=documents/");
        System.out.println("");
        System.out.println("📄 配置文件: file-transfer-server.yml");
        System.out.println("📁 数据目录: ./data/file-transfer/");
        System.out.println("📋 日志目录: ./logs/");
        System.out.println("===========================================");
    }
}